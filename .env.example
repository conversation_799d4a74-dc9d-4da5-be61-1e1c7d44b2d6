# 应用配置
NODE_ENV=production
PORT=5002
NEXT_TELEMETRY_DISABLED=1

# Docker 配置
DOCKERHUB_USERNAME=your-dockerhub-username
IMAGE_TAG=latest
HOST_PORT=5002

# 应用密钥（请在生产环境中更改）
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_URL=http://localhost:5002

# 数据库配置（如果使用）
# DATABASE_URL=postgresql://username:password@localhost:5432/notion_ai_study

# API 配置
# OPENAI_API_KEY=your-openai-api-key
# NOTION_API_KEY=your-notion-api-key

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 日志配置
LOG_LEVEL=info
LOG_DIR=./logs

# 安全配置
# JWT_SECRET=your-jwt-secret
# ENCRYPTION_KEY=your-encryption-key
