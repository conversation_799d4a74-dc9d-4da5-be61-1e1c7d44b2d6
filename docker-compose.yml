version: '3.8'

services:
  notion-ai-study:
    image: ${DOCKERHUB_USERNAME:-your-username}/notion-ai-study:${IMAGE_TAG:-latest}
    container_name: notion-ai-study-app
    ports:
      - "${HOST_PORT:-5002}:5002"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    env_file:
      - .env
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=5002
      - NEXT_TELEMETRY_DISABLED=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - notion-ai-study-network
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    # 安全配置
    security_opt:
      - no-new-privileges:true
    read_only: false  # Next.js 需要写入权限
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

networks:
  notion-ai-study-network:
    driver: bridge
