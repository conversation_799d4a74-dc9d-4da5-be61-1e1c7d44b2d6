# Docker 部署检查清单

## ✅ 部署前检查

### 环境准备
- [ ] Docker 已安装 (20.10+)
- [ ] Docker Compose 已安装 (2.0+)
- [ ] 网络连接正常

### 配置文件
- [ ] 下载 `.env.example` 并重命名为 `.env`（可选，也可直接使用 -e 参数）
- [ ] 配置必需的环境变量：
  - [ ] `STUDY_USERNAME` - 登录用户名
  - [ ] `STUDY_PASSWORD` - 登录密码
  - [ ] `NOTION_TOKEN` - Notion API Token
  - [ ] `DATABASE_ID` - Notion 数据库 ID
  - [ ] `VISION_API_KEY` - AI 视觉模型 API Key
  - [ ] `TEXT_API_KEY` - AI 文本模型 API Key

### 目录准备
- [ ] 创建 `uploads` 目录
- [ ] 创建 `logs` 目录
- [ ] 确保目录权限正确 (1001:1001)

## 🚀 部署步骤

### 方式一：直接命令行部署
```bash
# 创建目录
mkdir -p notion-ai-study && cd notion-ai-study
mkdir -p uploads logs

# 一键启动容器
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  -e STUDY_USERNAME=admin \
  -e STUDY_PASSWORD=your-password \
  -e NOTION_TOKEN=your-notion-token \
  -e DATABASE_ID=your-database-id \
  -e VISION_API_KEY=your-vision-api-key \
  -e TEXT_API_KEY=your-text-api-key \
  --restart unless-stopped \
  your-dockerhub-username/notion-ai-study:latest
```

### 方式二：使用环境文件部署
```bash
# 下载环境变量模板
curl -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/.env.example
cp .env.example .env && nano .env

# 创建目录并启动
mkdir -p uploads logs
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  --restart unless-stopped \
  your-dockerhub-username/notion-ai-study:latest
```

### 方式三：一键脚本部署
```bash
curl -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/quick-start.sh
chmod +x quick-start.sh
./quick-start.sh
```

## 🔍 部署后验证

### 服务状态检查
- [ ] 容器正常运行：`docker ps -f name=notion-ai-study`
- [ ] 健康检查通过：`curl http://localhost:5002/api/health`
- [ ] 应用可访问：`http://localhost:5002`
- [ ] 日志无错误：`docker logs notion-ai-study`

### 功能测试
- [ ] 登录功能正常
- [ ] 图片上传功能正常
- [ ] AI 分析功能正常
- [ ] Notion 同步功能正常
- [ ] 数据查看功能正常
- [ ] AI 总结功能正常

## 🛠️ 常用维护命令

```bash
# 查看容器状态
docker ps -f name=notion-ai-study

# 查看日志
docker logs -f notion-ai-study

# 重启服务
docker restart notion-ai-study

# 停止服务
docker stop notion-ai-study

# 删除容器
docker rm -f notion-ai-study

# 更新版本
docker pull your-dockerhub-username/notion-ai-study:latest
./quick-start.sh  # 或重新运行 docker run 命令

# 备份数据
tar -czf backup-$(date +%Y%m%d).tar.gz uploads/ logs/ .env
```

## ⚠️ 故障排除

### 常见问题

1. **容器启动失败**
   - 检查端口是否被占用
   - 检查环境变量配置
   - 查看容器日志

2. **AI 功能异常**
   - 验证 API Key 是否正确
   - 检查 API 配额是否充足
   - 确认网络连接正常

3. **Notion 同步失败**
   - 验证 Notion Token 权限
   - 检查数据库 ID 是否正确
   - 确认数据库结构匹配

4. **文件上传失败**
   - 检查 uploads 目录权限
   - 确认文件大小限制
   - 查看磁盘空间

### 调试命令

```bash
# 进入容器
docker exec -it notion-ai-study-app sh

# 查看资源使用
docker stats notion-ai-study-app

# 查看详细日志
docker logs --tail=100 -f notion-ai-study

# 测试网络连接
docker exec notion-ai-study-app curl -I https://api.openai.com
```

## 🔒 安全建议

- [ ] 更改默认用户名密码
- [ ] 使用强密码
- [ ] 定期更新镜像
- [ ] 配置防火墙
- [ ] 启用 HTTPS
- [ ] 定期备份数据
- [ ] 监控日志异常

## 📊 性能优化

- [ ] 调整容器资源限制
- [ ] 配置日志轮转
- [ ] 清理旧的上传文件
- [ ] 监控磁盘使用
- [ ] 优化网络配置
