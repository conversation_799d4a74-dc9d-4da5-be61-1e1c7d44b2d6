import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 启用 standalone 输出模式，用于 Docker 部署
  output: 'standalone',

  // 图片优化配置
  images: {
    unoptimized: true, // 在容器环境中禁用图片优化
  },

  // 服务器外部包配置
  serverExternalPackages: ['sharp'],

  // 跳过类型检查和ESLint检查（在Docker构建时）
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
};

export default nextConfig;
