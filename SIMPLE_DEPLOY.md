# 简化部署指南

## 🚀 最简单的部署方式

### 1. 下载配置文件

```bash
curl -O https://raw.githubusercontent.com/jhxxr/notion-ai-study/main/config/app.env
cp app.env .env
```

### 2. 编辑配置

```bash
nano .env
```

需要修改的关键配置：
- `STUDY_USERNAME` - 登录用户名
- `STUDY_PASSWORD` - 登录密码  
- `NOTION_TOKEN` - Notion API Token
- `DATABASE_ID` - Notion 数据库 ID
- `VISION_API_KEY` - AI 视觉模型 API Key
- `TEXT_API_KEY` - AI 文本模型 API Key

### 3. 一键启动

```bash
# 创建目录
mkdir -p /root/notion-ai-study/{uploads,logs}

# 启动容器
docker run -d \
  --name notion-ai-study \
  -p 62211:5002 \
  -v /root/notion-ai-study/uploads:/app/uploads \
  -v /root/notion-ai-study/logs:/app/logs \
  --env-file .env \
  --restart unless-stopped \
  jhxxr/notion-ai-study:latest
```

### 4. 访问应用

- 应用地址: http://localhost:62211
- 健康检查: http://localhost:62211/api/health

## 🔧 常用命令

```bash
# 查看容器状态
docker ps -f name=notion-ai-study

# 查看日志
docker logs -f notion-ai-study

# 重启容器
docker restart notion-ai-study

# 停止容器
docker stop notion-ai-study

# 删除容器
docker rm -f notion-ai-study

# 更新镜像
docker pull jhxxr/notion-ai-study:latest
docker stop notion-ai-study
docker rm notion-ai-study
# 然后重新运行上面的启动命令
```

## 📁 目录结构

```
/root/notion-ai-study/
├── .env              # 环境变量配置文件
├── uploads/          # 上传文件目录
└── logs/             # 日志文件目录
```

## 🛠️ 故障排除

### 容器无法启动
```bash
# 查看详细错误
docker logs notion-ai-study

# 检查端口是否被占用
netstat -tlnp | grep 62211
```

### AI 功能异常
- 检查 API Key 是否正确
- 确认网络连接正常
- 验证 API 配额是否充足

### Notion 同步失败
- 验证 Notion Token 权限
- 检查数据库 ID 是否正确
- 确认数据库结构匹配

就这么简单！🎉
