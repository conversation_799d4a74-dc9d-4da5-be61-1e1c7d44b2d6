(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[409],{646:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},903:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),s=t(2115),l=t(5695),d=t(5169),n=t(5040),c=t(5339),i=t(646);let o=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var x=t(3717);function u(){let[e,r]=(0,s.useState)("语文"),[t,u]=(0,s.useState)(""),[h,m]=(0,s.useState)("replace"),[g,b]=(0,s.useState)(!1),[p,y]=(0,s.useState)(""),[k,j]=(0,s.useState)(""),f=(0,l.useRouter)(),N=async r=>{if(r.preventDefault(),!t.trim())return void j("请输入学习内容");b(!0),j(""),y("");try{let r=await fetch("/api/study/update",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({subject:e,content:t.trim(),mode:h})}),a=await r.json();a.success?(y("".concat(e," 内容已成功保存到 Notion！")),"replace"===h&&u("")):j(a.message||"保存失败")}catch(e){j("网络错误，请稍后重试")}finally{b(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center h-16",children:[(0,a.jsxs)("button",{onClick:()=>f.push("/dashboard"),className:"flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"返回"})]}),(0,a.jsx)("h1",{className:"ml-4 text-xl font-bold text-gray-900 dark:text-white",children:"手动录入学习内容"})]})})}),(0,a.jsx)("main",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(n.A,{className:"w-6 h-6 text-blue-600 dark:text-blue-400 mr-3"}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"录入学习内容"})]}),(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"选择科目"}),(0,a.jsxs)("select",{value:e,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors",children:[x.u.map(e=>(0,a.jsx)("option",{value:e,children:e},e)),(0,a.jsx)("option",{value:"其他",children:"其他"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"更新模式"}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"replace",checked:"replace"===h,onChange:e=>m(e.target.value),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"替换内容"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",value:"append",checked:"append"===h,onChange:e=>m(e.target.value),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:"追加内容"})]})]}),(0,a.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:"replace"===h?"将完全替换该科目的现有内容":"将在现有内容后追加新内容"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"学习内容"}),(0,a.jsx)("textarea",{value:t,onChange:e=>u(e.target.value),rows:12,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors resize-vertical",placeholder:"请输入".concat(e,"的学习内容...\n\n例如：\n- 今日学习的知识点\n- 重要概念和定义\n- 练习题和解答\n- 学习心得和总结")}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between text-xs text-gray-500 dark:text-gray-400",children:[(0,a.jsx)("span",{children:"支持多行文本输入"}),(0,a.jsxs)("span",{children:[t.length," 字符"]})]})]}),k&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"w-5 h-5 text-red-500 mr-3"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-400",children:k})]})}),p&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,a.jsx)("p",{className:"text-green-700 dark:text-green-400",children:p})]})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:()=>{u(""),j(""),y("")},className:"flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"清空内容"}),(0,a.jsx)("button",{type:"submit",disabled:g||!t.trim(),className:"flex-1 flex justify-center items-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"保存中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o,{className:"w-4 h-4 mr-2"}),"保存到 Notion"]})})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:"\uD83D\uDCA1 使用说明"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 dark:text-blue-300 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"替换模式"}),"：完全替换该科目今日的所有内容"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"追加模式"}),"：在现有内容后添加新内容，用空行分隔"]}),(0,a.jsx)("li",{children:"• 内容将自动保存到您的 Notion 数据库中"}),(0,a.jsx)("li",{children:"• 支持多行文本，可以包含列表、段落等格式"})]})]})]})})]})}},3717:(e,r,t)=>{"use strict";t.d(r,{u:()=>a});let a=["数学","英语","政治","计算机408"]},5040:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5169:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5339:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}})},9223:(e,r,t)=>{Promise.resolve().then(t.bind(t,903))},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var a=t(2115);let s=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:i="",children:o,iconNode:x,...u}=e;return(0,a.createElement)("svg",{ref:r,...d,width:s,height:s,stroke:t,strokeWidth:c?24*Number(n)/Number(s):n,className:l("lucide",i),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(u)&&{"aria-hidden":"true"},...u},[...x.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(o)?o:[o]])}),c=(e,r)=>{let t=(0,a.forwardRef)((t,d)=>{let{className:c,...i}=t;return(0,a.createElement)(n,{ref:d,iconNode:r,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...i})});return t.displayName=s(e),t}}},e=>{e.O(0,[441,964,358],()=>e(e.s=9223)),_N_E=e.O()}]);