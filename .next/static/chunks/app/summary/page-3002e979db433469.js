(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[473],{646:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3904:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4351:(e,r,a)=>{Promise.resolve().then(a.bind(a,5413))},5169:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5413:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>u});var t=a(5155),s=a(2115),d=a(5695),l=a(5169),i=a(9376),c=a(3904),n=a(9946);let x=(0,n.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),h=(0,n.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var o=a(646);let m=(0,n.A)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]),p=(0,n.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function u(){let[e,r]=(0,s.useState)(""),[a,n]=(0,s.useState)(null),[u,g]=(0,s.useState)(!1),[b,y]=(0,s.useState)(!1),[k,j]=(0,s.useState)(""),[N,v]=(0,s.useState)(""),[w,f]=(0,s.useState)(!1),A=(0,d.useRouter)(),M=async()=>{g(!0),j(""),v("");try{let e=await fetch("/api/summary/generate",{method:"POST",headers:{"Content-Type":"application/json"}}),a=await e.json();a.success&&a.summary?(r(a.summary),n(a.data),y(!0),v(a.saved?"AI 总结生成成功并已保存到 Notion！":"AI 总结生成成功，但保存到 Notion 失败")):j(a.message||"生成总结失败")}catch(e){j("网络错误，请稍后重试")}finally{g(!1)}},C=async()=>{try{await navigator.clipboard.writeText(e),f(!0),setTimeout(()=>f(!1),2e3)}catch(e){j("复制失败，请手动选择文本复制")}};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex items-center h-16",children:[(0,t.jsxs)("button",{onClick:()=>A.push("/dashboard"),className:"flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,t.jsx)(l.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"返回"})]}),(0,t.jsx)("h1",{className:"ml-4 text-xl font-bold text-gray-900 dark:text-white",children:"AI 学习总结"})]})})}),(0,t.jsx)("main",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[a&&(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1",children:a.subjectsCount}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"已学科目"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400 mb-1",children:a.totalCharacters}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"总字符数"})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center",children:[(0,t.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1",children:b?"1":"0"}),(0,t.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"AI 总结"})]})]}),(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(i.A,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mr-3"}),(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"生成学习总结"})]}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"AI 将基于您今日的学习内容生成一份简洁、有条理的总结， 帮助您进行学习复盘和知识梳理。"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:M,disabled:u,className:"flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.A,{className:"w-4 h-4 animate-spin"}),(0,t.jsx)("span",{children:"生成中..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:b?"重新生成":"生成总结"})]})}),b&&(0,t.jsxs)("button",{onClick:C,className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,t.jsx)(x,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:w?"已复制!":"复制总结"})]})]})]}),k&&(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h,{className:"w-5 h-5 text-red-500 mr-3"}),(0,t.jsx)("p",{className:"text-red-700 dark:text-red-400",children:k})]})}),N&&(0,t.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,t.jsx)("p",{className:"text-green-700 dark:text-green-400",children:N})]})}),e&&(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(m,{className:"w-6 h-6 text-purple-600 dark:text-purple-400 mr-3"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"AI 总结结果"})]}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800",children:(0,t.jsx)("div",{className:"prose prose-sm max-w-none dark:prose-invert",children:(0,t.jsx)("div",{className:"text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap",children:e})})})]}),(0,t.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,t.jsxs)("div",{className:"flex items-center mb-4",children:[(0,t.jsx)(p,{className:"w-6 h-6 text-blue-600 dark:text-blue-400 mr-3"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-800 dark:text-blue-200",children:"使用说明"})]}),(0,t.jsxs)("div",{className:"space-y-4 text-sm text-blue-700 dark:text-blue-300",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"\uD83E\uDD16 AI 总结功能"}),(0,t.jsx)("p",{className:"mb-2",children:"AI 会分析您今日在各个科目的学习内容，生成一份简洁、有条理的学习总结，帮助您："}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[(0,t.jsx)("li",{children:"回顾今日学习的重点内容"}),(0,t.jsx)("li",{children:"梳理各科目的知识要点"}),(0,t.jsx)("li",{children:"发现学习中的关联性"}),(0,t.jsx)("li",{children:"为明日学习提供参考"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDCBE 自动保存"}),(0,t.jsx)("p",{children:"生成的总结会自动保存到您的 Notion 数据库中， 与当日的学习记录关联，方便后续查看和复习。"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"\uD83D\uDD04 重新生成"}),(0,t.jsx)("p",{children:'如果您对当前的总结不满意，可以点击"重新生成"按钮， AI 会基于最新的学习内容重新生成总结。'})]})]})]})]})})]})}},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}})},9376:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]])},9946:(e,r,a)=>{"use strict";a.d(r,{A:()=>c});var t=a(2115);let s=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,a)=>a?a.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},d=function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return r.filter((e,r,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,t.forwardRef)((e,r)=>{let{color:a="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:n="",children:x,iconNode:h,...o}=e;return(0,t.createElement)("svg",{ref:r,...l,width:s,height:s,stroke:a,strokeWidth:c?24*Number(i)/Number(s):i,className:d("lucide",n),...!x&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(o)&&{"aria-hidden":"true"},...o},[...h.map(e=>{let[r,a]=e;return(0,t.createElement)(r,a)}),...Array.isArray(x)?x:[x]])}),c=(e,r)=>{let a=(0,t.forwardRef)((a,l)=>{let{className:c,...n}=a;return(0,t.createElement)(i,{ref:l,iconNode:r,className:d("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...n})});return a.displayName=s(e),a}}},e=>{e.O(0,[441,964,358],()=>e(e.s=4351)),_N_E=e.O()}]);