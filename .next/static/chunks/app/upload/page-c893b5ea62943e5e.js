(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[994],{646:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2657:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5169:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5339:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}})},9230:(e,r,t)=>{Promise.resolve().then(t.bind(t,9605))},9605:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),s=t(2115),l=t(5695),d=t(5169),i=t(9869),n=t(9946);let c=(0,n.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),o=(0,n.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var x=t(2657),m=t(5339),h=t(646);function u(){let[e,r]=(0,s.useState)(null),[t,n]=(0,s.useState)(""),[u,g]=(0,s.useState)(!1),[b,p]=(0,s.useState)(null),[y,k]=(0,s.useState)(""),[f,j]=(0,s.useState)(""),v=(0,s.useRef)(null),N=(0,l.useRouter)(),w=e=>{if(!e.type.startsWith("image/"))return void k("请选择图片文件");if(e.size>0xa00000)return void k("图片文件过大，请选择小于10MB的图片");r(e),k(""),j(""),p(null);let t=new FileReader;t.onload=e=>{var r;n(null==(r=e.target)?void 0:r.result)},t.readAsDataURL(e)},A=async()=>{if(e){g(!0),k(""),j("");try{let r=new FormData;r.append("image",e);let t=await fetch("/api/upload",{method:"POST",body:r}),a=await t.json();a.success?(p(a.data.analysis),j("图片上传和分析成功！内容已保存到 Notion。")):k(a.message||"上传失败")}catch(e){k("网络错误，请稍后重试")}finally{g(!1)}}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center h-16",children:[(0,a.jsxs)("button",{onClick:()=>N.push("/dashboard"),className:"flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"返回"})]}),(0,a.jsx)("h1",{className:"ml-4 text-xl font-bold text-gray-900 dark:text-white",children:"图片上传分析"})]})})}),(0,a.jsx)("main",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"上传学习图片"}),e?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("img",{src:t,alt:"预览",className:"max-w-full h-auto max-h-96 mx-auto rounded-lg shadow"}),(0,a.jsx)("button",{onClick:()=>{r(null),n(""),p(null),k(""),j(""),v.current&&(v.current.value="")},className:"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors",children:(0,a.jsx)(c,{className:"w-4 h-4"})})]}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(o,{className:"w-5 h-5 text-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(e.size/1024/1024).toFixed(2)," MB"]})]})]})}),(0,a.jsx)("button",{onClick:A,disabled:u,className:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"AI 分析中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"开始 AI 分析"]})})]}):(0,a.jsxs)("div",{onDrop:e=>{e.preventDefault();let r=Array.from(e.dataTransfer.files);r.length>0&&w(r[0])},onDragOver:e=>{e.preventDefault()},className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center hover:border-blue-500 dark:hover:border-blue-400 transition-colors cursor-pointer",onClick:()=>{var e;return null==(e=v.current)?void 0:e.click()},children:[(0,a.jsx)(i.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-2",children:"拖拽图片到这里，或点击选择文件"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"支持 JPG、PNG、GIF 格式，最大 10MB"}),(0,a.jsx)("input",{ref:v,type:"file",accept:"image/*",onChange:e=>{var r;return(null==(r=e.target.files)?void 0:r[0])&&w(e.target.files[0])},className:"hidden"})]})]}),y&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-red-500 mr-3"}),(0,a.jsx)("p",{className:"text-red-700 dark:text-red-400",children:y})]})}),f&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"w-5 h-5 text-green-500 mr-3"}),(0,a.jsx)("p",{className:"text-green-700 dark:text-green-400",children:f})]})}),b&&(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"AI 分析结果"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"识别科目"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-3 py-1 bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium",children:b.subject}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["置信度: ",(100*b.confidence).toFixed(1),"%"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"识别内容"}),(0,a.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-gray-900 dark:text-white whitespace-pre-wrap",children:b.content})})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>N.push("/dashboard"),className:"flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"返回首页"}),(0,a.jsx)("button",{onClick:()=>N.push("/data"),className:"flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看数据"})]})]})]})]})})]})}},9869:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(2115);let s=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:c="",children:o,iconNode:x,...m}=e;return(0,a.createElement)("svg",{ref:r,...d,width:s,height:s,stroke:t,strokeWidth:n?24*Number(i)/Number(s):i,className:l("lucide",c),...!o&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(m)&&{"aria-hidden":"true"},...m},[...x.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(o)?o:[o]])}),n=(e,r)=>{let t=(0,a.forwardRef)((t,d)=>{let{className:n,...c}=t;return(0,a.createElement)(i,{ref:d,iconNode:r,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),n),...c})});return t.displayName=s(e),t}}},e=>{e.O(0,[441,964,358],()=>e(e.s=9230)),_N_E=e.O()}]);