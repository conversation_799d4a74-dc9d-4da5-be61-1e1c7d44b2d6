(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3792:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(5155),u=s(2115),a=s(5695);function n(){let e=(0,a.useRouter)();return(0,u.useEffect)(()=>{e.push("/login")},[e]),(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"正在跳转到登录页面..."})]})})}},5695:(e,r,s)=>{"use strict";var t=s(8999);s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}})},9188:(e,r,s)=>{Promise.resolve().then(s.bind(s,3792))}},e=>{e.O(0,[441,964,358],()=>e(e.s=9188)),_N_E=e.O()}]);