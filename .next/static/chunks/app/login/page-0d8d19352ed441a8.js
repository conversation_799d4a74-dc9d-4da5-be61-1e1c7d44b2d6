(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{2657:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3252:(e,r,t)=>{Promise.resolve().then(t.bind(t,9985))},5040:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}})},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(2115);let s=e=>{let r=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase());return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:n,className:i="",children:c,iconNode:u,...h}=e;return(0,a.createElement)("svg",{ref:r,...d,width:s,height:s,stroke:t,strokeWidth:n?24*Number(o)/Number(s):o,className:l("lucide",i),...!c&&!(e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),n=(e,r)=>{let t=(0,a.forwardRef)((t,d)=>{let{className:n,...i}=t;return(0,a.createElement)(o,{ref:d,iconNode:r,className:l("lucide-".concat(s(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),n),...i})});return t.displayName=s(e),t}},9985:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),s=t(2115),l=t(5695),d=t(5040),o=t(9946);let n=(0,o.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var i=t(2657);let c=(0,o.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function u(){let[e,r]=(0,s.useState)(""),[t,o]=(0,s.useState)(""),[u,h]=(0,s.useState)(!1),[m,x]=(0,s.useState)(!1),[g,y]=(0,s.useState)(""),p=(0,l.useRouter)(),b=async r=>{r.preventDefault(),x(!0),y("");try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:e,password:t})}),a=await r.json();a.success?p.push("/dashboard"):y(a.message||"登录失败")}catch(e){y("网络错误，请稍后重试")}finally{x(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"学习管理系统"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:"请登录您的账户"})]}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 py-8 px-6 shadow-xl rounded-lg",children:(0,a.jsxs)("form",{className:"space-y-6",onSubmit:b,children:[g&&(0,a.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm",children:g}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"用户名"}),(0,a.jsx)("input",{id:"username",name:"username",type:"text",required:!0,value:e,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors",placeholder:"请输入用户名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"密码"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("input",{id:"password",name:"password",type:u?"text":"password",required:!0,value:t,onChange:e=>o(e.target.value),className:"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors",placeholder:"请输入密码"}),(0,a.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!u),children:u?(0,a.jsx)(n,{className:"h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"}):(0,a.jsx)(i.A,{className:"h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:m,className:"w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"登录中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c,{className:"h-4 w-4 mr-2"}),"登录"]})})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"学习管理系统 v2.0 - Next.js 版本"})})]})})}}},e=>{e.O(0,[441,964,358],()=>e(e.s=3252)),_N_E=e.O()}]);