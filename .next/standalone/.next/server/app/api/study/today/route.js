(()=>{var a={};a.id=171,a.ids=[171],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12909:(a,b,c)=>{"use strict";function d(a){let b=function(a){try{let b=a.cookies.get("auth-token")?.value;if(!b)return null;let[c,d]=Buffer.from(b,"base64").toString().split(":"),e=parseInt(d);if(Date.now()-e>864e5)return null;let f=process.env.STUDY_USERNAME||"admin";if(c!==f)return null;return{username:c}}catch(a){return console.error("验证令牌错误:",a),null}}(a);if(!b)throw Error("未授权访问");return b}c.d(b,{o:()=>d})},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35322:(a,b,c)=>{"use strict";c.d(b,{L:()=>g});var d=c(94612);let e="https://api.notion.com/v1";class f{constructor(){this.client={token:process.env.NOTION_TOKEN||"",databaseId:process.env.DATABASE_ID||""}}getHeaders(){return{Authorization:`Bearer ${this.client.token}`,"Content-Type":"application/json","Notion-Version":"2022-06-28"}}async getTodayData(){try{let a=new Date().toISOString().split("T")[0],b=(await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:a}}},{headers:this.getHeaders()})).data.results;if(0===b.length)return{success:!0,data:{},date:a};let c=b[0],f={};return["数学","英语","政治","计算机408"].forEach(a=>{let b=c.properties[a];b&&b.rich_text&&b.rich_text.length>0&&(f[a]=b.rich_text[0].plain_text)}),{success:!0,data:f,date:a}}catch(a){return console.error("获取今日数据失败:",a),{success:!1,message:"获取数据失败"}}}async updateStudyRecord(a,b,c="replace"){try{let f,g=new Date().toISOString().split("T")[0],h=await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:g}}},{headers:this.getHeaders()}),i="";if(h.data.results.length>0){if(f=h.data.results[0].id,"append"===c){let b=h.data.results[0].properties[a];b&&b.rich_text&&b.rich_text.length>0&&(i=b.rich_text[0].plain_text)}}else f=(await d.A.post(`${e}/pages`,{parent:{database_id:this.client.databaseId},properties:{日期:{date:{start:g}}}},{headers:this.getHeaders()})).data.id;let j="append"===c&&i?`${i}

${b}`:b;return await d.A.patch(`${e}/pages/${f}`,{properties:{[a]:{rich_text:[{text:{content:j}}]}}},{headers:this.getHeaders()}),{success:!0,message:"更新成功"}}catch(a){return console.error("更新学习记录失败:",a),{success:!1,message:"更新失败"}}}async saveSummary(a){try{let b=new Date().toISOString().split("T")[0],c=await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:b}}},{headers:this.getHeaders()});if(0===c.data.results.length)return{success:!1,message:"未找到今日记录"};let f=c.data.results[0].id;return await d.A.patch(`${e}/pages/${f}`,{properties:{"总结（AI生成）":{rich_text:[{text:{content:a.substring(0,2e3)}}]}}},{headers:this.getHeaders()}),{success:!0,message:"总结保存成功"}}catch(a){return console.error("保存总结失败:",a),{success:!1,message:"保存总结失败"}}}}let g=new f},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45955:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>D,patchFetch:()=>C,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var d={};c.r(d),c.d(d,{GET:()=>x});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12909),w=c(35322);async function x(a){try{(0,v.o)(a);let b=await w.L.getTodayData();if(!b.success)return u.NextResponse.json({success:!1,message:b.message||"获取数据失败"},{status:500});let c=b.data||{},d=0,e=0,f=0;["数学","英语","政治","计算机408"].forEach(a=>{d++,c[a]&&c[a].trim()&&(e++,f+=c[a].length)});let g={totalSubjects:d,hasContentCount:e,emptyCount:d-e,totalCharacters:f,completionRate:Math.round(e/d*100)};return u.NextResponse.json({success:!0,data:b.data,date:b.date,statistics:g,message:"获取今日数据成功"})}catch(a){if(console.error("获取今日数据错误:",a),a instanceof Error&&"未授权访问"===a.message)return u.NextResponse.json({success:!1,message:"未授权访问"},{status:401});return u.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/study/today/route",pathname:"/api/study/today",filename:"route",bundlePath:"app/api/study/today/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/0jhx/notion-ai-study/src/app/api/study/today/route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function C(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function D(a,b,c){var d;let e="/api/study/today/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,612],()=>b(b.s=45955));module.exports=c})();