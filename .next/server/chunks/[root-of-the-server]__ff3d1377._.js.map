{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/0jhx/notion-ai-study/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { username, password } = await request.json();\n\n    // 验证用户名和密码\n    const validUsername = process.env.STUDY_USERNAME || 'admin';\n    const validPassword = process.env.STUDY_PASSWORD || 'password';\n\n    if (username === validUsername && password === validPassword) {\n      // 创建简单的会话令牌\n      const token = Buffer.from(`${username}:${Date.now()}`).toString('base64');\n      \n      const response = NextResponse.json({\n        success: true,\n        message: '登录成功',\n        user: { username },\n        token\n      });\n\n      // 设置 HTTP-only cookie\n      response.cookies.set('auth-token', token, {\n        httpOnly: true,\n        secure: process.env.NODE_ENV === 'production',\n        sameSite: 'strict',\n        maxAge: 24 * 60 * 60 * 1000 // 24小时\n      });\n\n      return response;\n    } else {\n      return NextResponse.json({\n        success: false,\n        message: '用户名或密码错误'\n      }, { status: 401 });\n    }\n  } catch (error) {\n    console.error('登录错误:', error);\n    return NextResponse.json({\n      success: false,\n      message: '服务器错误'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,WAAW;QACX,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,YAAY;YACZ,MAAM,QAAQ,OAAO,IAAI,CAAC,GAAG,SAAS,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,CAAC;YAEhE,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACjC,SAAS;gBACT,SAAS;gBACT,MAAM;oBAAE;gBAAS;gBACjB;YACF;YAEA,sBAAsB;YACtB,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;gBACxC,UAAU;gBACV,QAAQ,oDAAyB;gBACjC,UAAU;gBACV,QAAQ,KAAK,KAAK,KAAK,KAAK,OAAO;YACrC;YAEA,OAAO;QACT,OAAO;YACL,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,SAAS;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}