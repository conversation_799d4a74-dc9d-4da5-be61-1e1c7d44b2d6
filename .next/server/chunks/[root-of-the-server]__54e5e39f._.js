module.exports = {

"[project]/.next-internal/server/app/api/summary/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "requireAuth": ()=>requireAuth,
    "verifyAuth": ()=>verifyAuth
});
function verifyAuth(request) {
    try {
        const token = request.cookies.get('auth-token')?.value;
        if (!token) {
            return null;
        }
        // 解码令牌
        const decoded = Buffer.from(token, 'base64').toString();
        const [username, timestamp] = decoded.split(':');
        // 检查令牌是否过期（24小时）
        const tokenTime = parseInt(timestamp);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        if (now - tokenTime > maxAge) {
            return null;
        }
        // 验证用户名
        const validUsername = process.env.STUDY_USERNAME || 'admin';
        if (username !== validUsername) {
            return null;
        }
        return {
            username
        };
    } catch (error) {
        console.error('验证令牌错误:', error);
        return null;
    }
}
function requireAuth(request) {
    const user = verifyAuth(request);
    if (!user) {
        throw new Error('未授权访问');
    }
    return user;
}
}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/lib/ai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "TextAI": ()=>TextAI,
    "VisionAI": ()=>VisionAI,
    "textAI": ()=>textAI,
    "visionAI": ()=>visionAI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
class VisionAI {
    apiKey;
    baseURL;
    constructor(){
        this.apiKey = process.env.VISION_API_KEY || '';
        this.baseURL = process.env.VISION_BASE_URL || '';
    }
    async analyzeImage(imageBase64) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${this.baseURL}/chat/completions`, {
                model: 'gpt-4-vision-preview',
                messages: [
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'text',
                                text: '请分析这张图片中的学习内容，识别出具体的科目和内容。请按照以下格式返回JSON：{"subject": "科目名称", "content": "具体内容", "confidence": 0.95}'
                            },
                            {
                                type: 'image_url',
                                image_url: {
                                    url: `data:image/jpeg;base64,${imageBase64}`
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 1000
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const content = response.data.choices[0].message.content;
            try {
                const analysis = JSON.parse(content);
                return {
                    success: true,
                    analysis
                };
            } catch  {
                // 如果不是 JSON 格式，返回原始文本
                return {
                    success: true,
                    analysis: {
                        subject: '未知',
                        content: content,
                        confidence: 0.5
                    }
                };
            }
        } catch (error) {
            console.error('AI 视觉分析失败:', error);
            return {
                success: false,
                message: 'AI 分析失败'
            };
        }
    }
}
class TextAI {
    apiKey;
    baseURL;
    constructor(){
        this.apiKey = process.env.TEXT_API_KEY || '';
        this.baseURL = process.env.TEXT_BASE_URL || '';
    }
    async generateSummary(studyData) {
        try {
            const subjects = Object.keys(studyData).filter((key)=>studyData[key] && studyData[key].trim());
            if (subjects.length === 0) {
                return {
                    success: false,
                    message: '没有学习内容可以总结'
                };
            }
            const prompt = `请基于以下学习内容生成一份简洁、有条理的学习总结：

${subjects.map((subject)=>`**${subject}**:\n${studyData[subject]}`).join('\n\n')}

请生成一份包含以下内容的总结：
1. 今日学习概览
2. 各科目重点内容
3. 知识点关联分析
4. 学习建议

总结应该简洁明了，重点突出，有助于复习和巩固。`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${this.baseURL}/chat/completions`, {
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的学习助手，擅长分析和总结学习内容，帮助学生更好地理解和记忆知识点。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1500,
                temperature: 0.7
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                }
            });
            const summary = response.data.choices[0].message.content;
            return {
                success: true,
                summary
            };
        } catch (error) {
            console.error('AI 总结生成失败:', error);
            return {
                success: false,
                message: 'AI 总结生成失败'
            };
        }
    }
}
const visionAI = new VisionAI();
const textAI = new TextAI();
}),
"[project]/src/lib/notion.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "notionService": ()=>notionService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const NOTION_API_BASE = 'https://api.notion.com/v1';
class NotionService {
    client;
    constructor(){
        this.client = {
            token: process.env.NOTION_TOKEN || '',
            databaseId: process.env.DATABASE_ID || ''
        };
    }
    getHeaders() {
        return {
            'Authorization': `Bearer ${this.client.token}`,
            'Content-Type': 'application/json',
            'Notion-Version': '2022-06-28'
        };
    }
    // 获取今日数据
    async getTodayData() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
                filter: {
                    property: '日期',
                    date: {
                        equals: today
                    }
                }
            }, {
                headers: this.getHeaders()
            });
            const results = response.data.results;
            if (results.length === 0) {
                return {
                    success: true,
                    data: {},
                    date: today
                };
            }
            const record = results[0];
            const data = {};
            // 提取各科目数据 - 考研科目
            const subjects = [
                '数学',
                '英语',
                '政治',
                '计算机408'
            ];
            subjects.forEach((subject)=>{
                const property = record.properties[subject];
                if (property && property.rich_text && property.rich_text.length > 0) {
                    data[subject] = property.rich_text[0].plain_text;
                }
            });
            return {
                success: true,
                data,
                date: today
            };
        } catch (error) {
            console.error('获取今日数据失败:', error);
            return {
                success: false,
                message: '获取数据失败'
            };
        }
    }
    // 更新学习记录
    async updateStudyRecord(subject, content, mode = 'replace') {
        try {
            const today = new Date().toISOString().split('T')[0];
            // 查找今日记录
            const queryResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
                filter: {
                    property: '日期',
                    date: {
                        equals: today
                    }
                }
            }, {
                headers: this.getHeaders()
            });
            let pageId;
            let existingContent = '';
            if (queryResponse.data.results.length > 0) {
                // 更新现有记录
                pageId = queryResponse.data.results[0].id;
                if (mode === 'append') {
                    const existingProperty = queryResponse.data.results[0].properties[subject];
                    if (existingProperty && existingProperty.rich_text && existingProperty.rich_text.length > 0) {
                        existingContent = existingProperty.rich_text[0].plain_text;
                    }
                }
            } else {
                // 创建新记录
                const createResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${NOTION_API_BASE}/pages`, {
                    parent: {
                        database_id: this.client.databaseId
                    },
                    properties: {
                        '日期': {
                            date: {
                                start: today
                            }
                        }
                    }
                }, {
                    headers: this.getHeaders()
                });
                pageId = createResponse.data.id;
            }
            // 更新内容
            const finalContent = mode === 'append' && existingContent ? `${existingContent}\n\n${content}` : content;
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].patch(`${NOTION_API_BASE}/pages/${pageId}`, {
                properties: {
                    [subject]: {
                        rich_text: [
                            {
                                text: {
                                    content: finalContent
                                }
                            }
                        ]
                    }
                }
            }, {
                headers: this.getHeaders()
            });
            return {
                success: true,
                message: '更新成功'
            };
        } catch (error) {
            console.error('更新学习记录失败:', error);
            return {
                success: false,
                message: '更新失败'
            };
        }
    }
    // 保存 AI 总结
    async saveSummary(summary) {
        try {
            const today = new Date().toISOString().split('T')[0];
            // 查找今日记录
            const queryResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
                filter: {
                    property: '日期',
                    date: {
                        equals: today
                    }
                }
            }, {
                headers: this.getHeaders()
            });
            if (queryResponse.data.results.length === 0) {
                return {
                    success: false,
                    message: '未找到今日记录'
                };
            }
            const pageId = queryResponse.data.results[0].id;
            // 更新总结字段 - 使用正确的字段名称
            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].patch(`${NOTION_API_BASE}/pages/${pageId}`, {
                properties: {
                    '总结（AI生成）': {
                        rich_text: [
                            {
                                text: {
                                    content: summary.substring(0, 2000)
                                } // 限制长度防止超出Notion限制
                            }
                        ]
                    }
                }
            }, {
                headers: this.getHeaders()
            });
            return {
                success: true,
                message: '总结保存成功'
            };
        } catch (error) {
            console.error('保存总结失败:', error);
            return {
                success: false,
                message: '保存总结失败'
            };
        }
    }
}
const notionService = new NotionService();
}),
"[project]/src/app/api/summary/generate/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notion$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/notion.ts [app-route] (ecmascript)");
;
;
;
;
async function POST(request) {
    try {
        // 验证用户身份
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAuth"])(request);
        // 获取今日学习数据
        const todayDataResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notion$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["notionService"].getTodayData();
        if (!todayDataResult.success) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '获取今日学习数据失败'
            }, {
                status: 500
            });
        }
        const studyData = todayDataResult.data || {};
        // 检查是否有学习内容
        const hasContent = Object.values(studyData).some((content)=>content && typeof content === 'string' && content.trim());
        if (!hasContent) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '今日暂无学习内容，无法生成总结'
            }, {
                status: 400
            });
        }
        // 生成 AI 总结
        const summaryResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["textAI"].generateSummary(studyData);
        if (!summaryResult.success) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: summaryResult.message || 'AI 总结生成失败'
            }, {
                status: 500
            });
        }
        const summary = summaryResult.summary;
        // 保存总结到 Notion
        const saveResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$notion$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["notionService"].saveSummary(summary);
        if (!saveResult.success) {
            console.warn('保存总结到 Notion 失败:', saveResult.message);
        // 即使保存失败，也返回生成的总结
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            summary,
            saved: saveResult.success,
            message: saveResult.success ? 'AI 总结生成并保存成功' : 'AI 总结生成成功，但保存失败',
            data: {
                date: todayDataResult.date,
                subjectsCount: Object.keys(studyData).filter((key)=>studyData[key] && studyData[key].trim()).length,
                totalCharacters: Object.values(studyData).filter((content)=>content && typeof content === 'string').reduce((total, content)=>total + content.length, 0)
            }
        });
    } catch (error) {
        console.error('生成 AI 总结错误:', error);
        if (error instanceof Error && error.message === '未授权访问') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: '未授权访问'
            }, {
                status: 401
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: '服务器错误'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__54e5e39f._.js.map