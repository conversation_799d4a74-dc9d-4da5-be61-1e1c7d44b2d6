{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/0jhx/notion-ai-study/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\n\nexport interface User {\n  username: string;\n}\n\nexport function verifyAuth(request: NextRequest): User | null {\n  try {\n    const token = request.cookies.get('auth-token')?.value;\n    \n    if (!token) {\n      return null;\n    }\n\n    // 解码令牌\n    const decoded = Buffer.from(token, 'base64').toString();\n    const [username, timestamp] = decoded.split(':');\n    \n    // 检查令牌是否过期（24小时）\n    const tokenTime = parseInt(timestamp);\n    const now = Date.now();\n    const maxAge = 24 * 60 * 60 * 1000; // 24小时\n    \n    if (now - tokenTime > maxAge) {\n      return null;\n    }\n\n    // 验证用户名\n    const validUsername = process.env.STUDY_USERNAME || 'admin';\n    if (username !== validUsername) {\n      return null;\n    }\n\n    return { username };\n  } catch (error) {\n    console.error('验证令牌错误:', error);\n    return null;\n  }\n}\n\nexport function requireAuth(request: NextRequest) {\n  const user = verifyAuth(request);\n  if (!user) {\n    throw new Error('未授权访问');\n  }\n  return user;\n}\n"], "names": [], "mappings": ";;;;AAMO,SAAS,WAAW,OAAoB;IAC7C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO;QACT;QAEA,OAAO;QACP,MAAM,UAAU,OAAO,IAAI,CAAC,OAAO,UAAU,QAAQ;QACrD,MAAM,CAAC,UAAU,UAAU,GAAG,QAAQ,KAAK,CAAC;QAE5C,iBAAiB;QACjB,MAAM,YAAY,SAAS;QAC3B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,SAAS,KAAK,KAAK,KAAK,MAAM,OAAO;QAE3C,IAAI,MAAM,YAAY,QAAQ;YAC5B,OAAO;QACT;QAEA,QAAQ;QACR,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT;QAEA,OAAO;YAAE;QAAS;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,WAAW;QACzB,OAAO;IACT;AACF;AAEO,SAAS,YAAY,OAAoB;IAC9C,MAAM,OAAO,WAAW;IACxB,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/0jhx/notion-ai-study/src/lib/notion.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst NOTION_API_BASE = 'https://api.notion.com/v1';\n\ninterface NotionClient {\n  token: string;\n  databaseId: string;\n}\n\nclass NotionService {\n  private client: NotionClient;\n\n  constructor() {\n    this.client = {\n      token: process.env.NOTION_TOKEN || '',\n      databaseId: process.env.DATABASE_ID || ''\n    };\n  }\n\n  private getHeaders() {\n    return {\n      'Authorization': `Bearer ${this.client.token}`,\n      'Content-Type': 'application/json',\n      'Notion-Version': '2022-06-28'\n    };\n  }\n\n  // 获取今日数据\n  async getTodayData(): Promise<{ success: boolean; data?: any; date?: string; message?: string }> {\n    try {\n      const today = new Date().toISOString().split('T')[0];\n      \n      const response = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {\n        filter: {\n          property: '日期',\n          date: {\n            equals: today\n          }\n        }\n      }, { headers: this.getHeaders() });\n\n      const results = response.data.results;\n      \n      if (results.length === 0) {\n        return { success: true, data: {}, date: today };\n      }\n\n      const record = results[0];\n      const data: Record<string, string> = {};\n\n      // 提取各科目数据 - 考研科目\n      const subjects = ['数学', '英语', '政治', '计算机408'];\n      subjects.forEach(subject => {\n        const property = record.properties[subject];\n        if (property && property.rich_text && property.rich_text.length > 0) {\n          data[subject] = property.rich_text[0].plain_text;\n        }\n      });\n\n      return { success: true, data, date: today };\n    } catch (error) {\n      console.error('获取今日数据失败:', error);\n      return { success: false, message: '获取数据失败' };\n    }\n  }\n\n  // 更新学习记录\n  async updateStudyRecord(subject: string, content: string, mode: 'replace' | 'append' = 'replace'): Promise<{ success: boolean; message?: string }> {\n    try {\n      const today = new Date().toISOString().split('T')[0];\n      \n      // 查找今日记录\n      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {\n        filter: {\n          property: '日期',\n          date: {\n            equals: today\n          }\n        }\n      }, { headers: this.getHeaders() });\n\n      let pageId: string;\n      let existingContent = '';\n\n      if (queryResponse.data.results.length > 0) {\n        // 更新现有记录\n        pageId = queryResponse.data.results[0].id;\n        \n        if (mode === 'append') {\n          const existingProperty = queryResponse.data.results[0].properties[subject];\n          if (existingProperty && existingProperty.rich_text && existingProperty.rich_text.length > 0) {\n            existingContent = existingProperty.rich_text[0].plain_text;\n          }\n        }\n      } else {\n        // 创建新记录\n        const createResponse = await axios.post(`${NOTION_API_BASE}/pages`, {\n          parent: { database_id: this.client.databaseId },\n          properties: {\n            '日期': {\n              date: { start: today }\n            }\n          }\n        }, { headers: this.getHeaders() });\n        \n        pageId = createResponse.data.id;\n      }\n\n      // 更新内容\n      const finalContent = mode === 'append' && existingContent \n        ? `${existingContent}\\n\\n${content}`\n        : content;\n\n      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {\n        properties: {\n          [subject]: {\n            rich_text: [\n              {\n                text: { content: finalContent }\n              }\n            ]\n          }\n        }\n      }, { headers: this.getHeaders() });\n\n      return { success: true, message: '更新成功' };\n    } catch (error) {\n      console.error('更新学习记录失败:', error);\n      return { success: false, message: '更新失败' };\n    }\n  }\n\n  // 保存 AI 总结\n  async saveSummary(summary: string): Promise<{ success: boolean; message?: string }> {\n    try {\n      const today = new Date().toISOString().split('T')[0];\n      \n      // 查找今日记录\n      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {\n        filter: {\n          property: '日期',\n          date: {\n            equals: today\n          }\n        }\n      }, { headers: this.getHeaders() });\n\n      if (queryResponse.data.results.length === 0) {\n        return { success: false, message: '未找到今日记录' };\n      }\n\n      const pageId = queryResponse.data.results[0].id;\n\n      // 更新总结字段 - 使用正确的字段名称\n      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {\n        properties: {\n          '总结（AI生成）': {\n            rich_text: [\n              {\n                text: { content: summary.substring(0, 2000) } // 限制长度防止超出Notion限制\n              }\n            ]\n          }\n        }\n      }, { headers: this.getHeaders() });\n\n      return { success: true, message: '总结保存成功' };\n    } catch (error) {\n      console.error('保存总结失败:', error);\n      return { success: false, message: '保存总结失败' };\n    }\n  }\n}\n\nexport const notionService = new NotionService();\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAOxB,MAAM;IACI,OAAqB;IAE7B,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG;YACZ,OAAO,QAAQ,GAAG,CAAC,YAAY,IAAI;YACnC,YAAY,QAAQ,GAAG,CAAC,WAAW,IAAI;QACzC;IACF;IAEQ,aAAa;QACnB,OAAO;YACL,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC9C,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,SAAS;IACT,MAAM,eAA2F;QAC/F,IAAI;YACF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAChG,QAAQ;oBACN,UAAU;oBACV,MAAM;wBACJ,QAAQ;oBACV;gBACF;YACF,GAAG;gBAAE,SAAS,IAAI,CAAC,UAAU;YAAG;YAEhC,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO;YAErC,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,OAAO;oBAAE,SAAS;oBAAM,MAAM,CAAC;oBAAG,MAAM;gBAAM;YAChD;YAEA,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,OAA+B,CAAC;YAEtC,iBAAiB;YACjB,MAAM,WAAW;gBAAC;gBAAM;gBAAM;gBAAM;aAAS;YAC7C,SAAS,OAAO,CAAC,CAAA;gBACf,MAAM,WAAW,OAAO,UAAU,CAAC,QAAQ;gBAC3C,IAAI,YAAY,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;oBACnE,IAAI,CAAC,QAAQ,GAAG,SAAS,SAAS,CAAC,EAAE,CAAC,UAAU;gBAClD;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM;gBAAM,MAAM;YAAM;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAS;QAC7C;IACF;IAEA,SAAS;IACT,MAAM,kBAAkB,OAAe,EAAE,OAAe,EAAE,OAA6B,SAAS,EAAmD;QACjJ,IAAI;YACF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,SAAS;YACT,MAAM,gBAAgB,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBACrG,QAAQ;oBACN,UAAU;oBACV,MAAM;wBACJ,QAAQ;oBACV;gBACF;YACF,GAAG;gBAAE,SAAS,IAAI,CAAC,UAAU;YAAG;YAEhC,IAAI;YACJ,IAAI,kBAAkB;YAEtB,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;gBACzC,SAAS;gBACT,SAAS,cAAc,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBAEzC,IAAI,SAAS,UAAU;oBACrB,MAAM,mBAAmB,cAAc,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ;oBAC1E,IAAI,oBAAoB,iBAAiB,SAAS,IAAI,iBAAiB,SAAS,CAAC,MAAM,GAAG,GAAG;wBAC3F,kBAAkB,iBAAiB,SAAS,CAAC,EAAE,CAAC,UAAU;oBAC5D;gBACF;YACF,OAAO;gBACL,QAAQ;gBACR,MAAM,iBAAiB,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,MAAM,CAAC,EAAE;oBAClE,QAAQ;wBAAE,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU;oBAAC;oBAC9C,YAAY;wBACV,MAAM;4BACJ,MAAM;gCAAE,OAAO;4BAAM;wBACvB;oBACF;gBACF,GAAG;oBAAE,SAAS,IAAI,CAAC,UAAU;gBAAG;gBAEhC,SAAS,eAAe,IAAI,CAAC,EAAE;YACjC;YAEA,OAAO;YACP,MAAM,eAAe,SAAS,YAAY,kBACtC,GAAG,gBAAgB,IAAI,EAAE,SAAS,GAClC;YAEJ,MAAM,uIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,gBAAgB,OAAO,EAAE,QAAQ,EAAE;gBACtD,YAAY;oBACV,CAAC,QAAQ,EAAE;wBACT,WAAW;4BACT;gCACE,MAAM;oCAAE,SAAS;gCAAa;4BAChC;yBACD;oBACH;gBACF;YACF,GAAG;gBAAE,SAAS,IAAI,CAAC,UAAU;YAAG;YAEhC,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAO;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAO;QAC3C;IACF;IAEA,WAAW;IACX,MAAM,YAAY,OAAe,EAAmD;QAClF,IAAI;YACF,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEpD,SAAS;YACT,MAAM,gBAAgB,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,gBAAgB,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBACrG,QAAQ;oBACN,UAAU;oBACV,MAAM;wBACJ,QAAQ;oBACV;gBACF;YACF,GAAG;gBAAE,SAAS,IAAI,CAAC,UAAU;YAAG;YAEhC,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAC3C,OAAO;oBAAE,SAAS;oBAAO,SAAS;gBAAU;YAC9C;YAEA,MAAM,SAAS,cAAc,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YAE/C,qBAAqB;YACrB,MAAM,uIAAA,CAAA,UAAK,CAAC,KAAK,CAAC,GAAG,gBAAgB,OAAO,EAAE,QAAQ,EAAE;gBACtD,YAAY;oBACV,YAAY;wBACV,WAAW;4BACT;gCACE,MAAM;oCAAE,SAAS,QAAQ,SAAS,CAAC,GAAG;gCAAM,EAAE,mBAAmB;4BACnE;yBACD;oBACH;gBACF;YACF,GAAG;gBAAE,SAAS,IAAI,CAAC,UAAU;YAAG;YAEhC,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAS;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,OAAO;gBAAE,SAAS;gBAAO,SAAS;YAAS;QAC7C;IACF;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/0jhx/notion-ai-study/src/app/api/study/today/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { requireAuth } from '@/lib/auth';\nimport { notionService } from '@/lib/notion';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // 验证用户身份\n    requireAuth(request);\n\n    // 获取今日数据\n    const result = await notionService.getTodayData();\n\n    if (!result.success) {\n      return NextResponse.json({\n        success: false,\n        message: result.message || '获取数据失败'\n      }, { status: 500 });\n    }\n\n    // 计算统计信息 - 考研科目\n    const data = result.data || {};\n    const subjects = ['数学', '英语', '政治', '计算机408'];\n\n    let totalSubjects = 0;\n    let hasContentCount = 0;\n    let totalCharacters = 0;\n\n    subjects.forEach(subject => {\n      totalSubjects++;\n      if (data[subject] && data[subject].trim()) {\n        hasContentCount++;\n        totalCharacters += data[subject].length;\n      }\n    });\n\n    const statistics = {\n      totalSubjects,\n      hasContentCount,\n      emptyCount: totalSubjects - hasContentCount,\n      totalCharacters,\n      completionRate: Math.round((hasContentCount / totalSubjects) * 100)\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: result.data,\n      date: result.date,\n      statistics,\n      message: '获取今日数据成功'\n    });\n\n  } catch (error) {\n    console.error('获取今日数据错误:', error);\n    \n    if (error instanceof Error && error.message === '未授权访问') {\n      return NextResponse.json({\n        success: false,\n        message: '未授权访问'\n      }, { status: 401 });\n    }\n\n    return NextResponse.json({\n      success: false,\n      message: '服务器错误'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,SAAS;QACT,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE;QAEZ,SAAS;QACT,MAAM,SAAS,MAAM,sHAAA,CAAA,gBAAa,CAAC,YAAY;QAE/C,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS,OAAO,OAAO,IAAI;YAC7B,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,gBAAgB;QAChB,MAAM,OAAO,OAAO,IAAI,IAAI,CAAC;QAC7B,MAAM,WAAW;YAAC;YAAM;YAAM;YAAM;SAAS;QAE7C,IAAI,gBAAgB;QACpB,IAAI,kBAAkB;QACtB,IAAI,kBAAkB;QAEtB,SAAS,OAAO,CAAC,CAAA;YACf;YACA,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;gBACzC;gBACA,mBAAmB,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzC;QACF;QAEA,MAAM,aAAa;YACjB;YACA;YACA,YAAY,gBAAgB;YAC5B;YACA,gBAAgB,KAAK,KAAK,CAAC,AAAC,kBAAkB,gBAAiB;QACjE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB;YACA,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAE3B,IAAI,iBAAiB,SAAS,MAAM,OAAO,KAAK,SAAS;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}