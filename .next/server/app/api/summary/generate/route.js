(()=>{var a={};a.id=656,a.ids=[656],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12909:(a,b,c)=>{"use strict";function d(a){let b=function(a){try{let b=a.cookies.get("auth-token")?.value;if(!b)return null;let[c,d]=Buffer.from(b,"base64").toString().split(":"),e=parseInt(d);if(Date.now()-e>864e5)return null;let f=process.env.STUDY_USERNAME||"admin";if(c!==f)return null;return{username:c}}catch(a){return console.error("验证令牌错误:",a),null}}(a);if(!b)throw Error("未授权访问");return b}c.d(b,{o:()=>d})},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},35322:(a,b,c)=>{"use strict";c.d(b,{L:()=>g});var d=c(94612);let e="https://api.notion.com/v1";class f{constructor(){this.client={token:process.env.NOTION_TOKEN||"",databaseId:process.env.DATABASE_ID||""}}getHeaders(){return{Authorization:`Bearer ${this.client.token}`,"Content-Type":"application/json","Notion-Version":"2022-06-28"}}async getTodayData(){try{let a=new Date().toISOString().split("T")[0],b=(await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:a}}},{headers:this.getHeaders()})).data.results;if(0===b.length)return{success:!0,data:{},date:a};let c=b[0],f={};return["数学","英语","政治","计算机408"].forEach(a=>{let b=c.properties[a];b&&b.rich_text&&b.rich_text.length>0&&(f[a]=b.rich_text[0].plain_text)}),{success:!0,data:f,date:a}}catch(a){return console.error("获取今日数据失败:",a),{success:!1,message:"获取数据失败"}}}async updateStudyRecord(a,b,c="replace"){try{let f,g=new Date().toISOString().split("T")[0],h=await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:g}}},{headers:this.getHeaders()}),i="";if(h.data.results.length>0){if(f=h.data.results[0].id,"append"===c){let b=h.data.results[0].properties[a];b&&b.rich_text&&b.rich_text.length>0&&(i=b.rich_text[0].plain_text)}}else f=(await d.A.post(`${e}/pages`,{parent:{database_id:this.client.databaseId},properties:{日期:{date:{start:g}}}},{headers:this.getHeaders()})).data.id;let j="append"===c&&i?`${i}

${b}`:b;return await d.A.patch(`${e}/pages/${f}`,{properties:{[a]:{rich_text:[{text:{content:j}}]}}},{headers:this.getHeaders()}),{success:!0,message:"更新成功"}}catch(a){return console.error("更新学习记录失败:",a),{success:!1,message:"更新失败"}}}async saveSummary(a){try{let b=new Date().toISOString().split("T")[0],c=await d.A.post(`${e}/databases/${this.client.databaseId}/query`,{filter:{property:"日期",date:{equals:b}}},{headers:this.getHeaders()});if(0===c.data.results.length)return{success:!1,message:"未找到今日记录"};let f=c.data.results[0].id;return await d.A.patch(`${e}/pages/${f}`,{properties:{"总结（AI生成）":{rich_text:[{text:{content:a.substring(0,2e3)}}]}}},{headers:this.getHeaders()}),{success:!0,message:"总结保存成功"}}catch(a){return console.error("保存总结失败:",a),{success:!1,message:"保存总结失败"}}}}let g=new f},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93209:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{POST:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(12909),w=c(98949),x=c(35322);async function y(a){try{(0,v.o)(a);let b=await x.L.getTodayData();if(!b.success)return u.NextResponse.json({success:!1,message:"获取今日学习数据失败"},{status:500});let c=b.data||{};if(!Object.values(c).some(a=>a&&"string"==typeof a&&a.trim()))return u.NextResponse.json({success:!1,message:"今日暂无学习内容，无法生成总结"},{status:400});let d=await w.Uj.generateSummary(c);if(!d.success)return u.NextResponse.json({success:!1,message:d.message||"AI 总结生成失败"},{status:500});let e=d.summary,f=await x.L.saveSummary(e);return f.success||console.warn("保存总结到 Notion 失败:",f.message),u.NextResponse.json({success:!0,summary:e,saved:f.success,message:f.success?"AI 总结生成并保存成功":"AI 总结生成成功，但保存失败",data:{date:b.date,subjectsCount:Object.keys(c).filter(a=>c[a]&&c[a].trim()).length,totalCharacters:Object.values(c).filter(a=>a&&"string"==typeof a).reduce((a,b)=>a+b.length,0)}})}catch(a){if(console.error("生成 AI 总结错误:",a),a instanceof Error&&"未授权访问"===a.message)return u.NextResponse.json({success:!1,message:"未授权访问"},{status:401});return u.NextResponse.json({success:!1,message:"服务器错误"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/summary/generate/route",pathname:"/api/summary/generate",filename:"route",bundlePath:"app/api/summary/generate/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/0jhx/notion-ai-study/src/app/api/summary/generate/route.ts",nextConfigOutput:"standalone",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/summary/generate/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{},98949:(a,b,c)=>{"use strict";c.d(b,{Uj:()=>h,xR:()=>g});var d=c(94612);class e{constructor(){this.apiKey=process.env.VISION_API_KEY||"",this.baseURL=process.env.VISION_BASE_URL||"https://api.openai.com/v1",this.model=process.env.VISION_MODEL||"gpt-4-vision-preview"}async analyzeImage(a){try{let b=(await d.A.post(`${this.baseURL}/chat/completions`,{model:this.model,messages:[{role:"user",content:[{type:"text",text:`请分析这张图片中的学习内容，识别出具体的科目和内容。请按照以下格式返回JSON：

{
  "subject": "科目名称（数学/英语/政治/计算机408中的一个）",
  "content": "## 📸 图片分析记录 - ${new Date().toISOString().split("T")[0]}

**科目：** [科目名称]

**学习内容：**

[详细的学习内容描述]

**图片描述：**

[对图片内容的详细描述]",
  "confidence": 0.95
}

请确保内容详细且有条理，图片描述要准确反映图片中的具体内容。`},{type:"image_url",image_url:{url:`data:image/jpeg;base64,${a}`}}]}],max_tokens:1e3},{headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}})).data.choices[0].message.content;try{let a=JSON.parse(b);return{success:!0,analysis:a}}catch{return{success:!0,analysis:{subject:"未知",content:b,confidence:.5}}}}catch(a){return console.error("AI 视觉分析失败:",a),{success:!1,message:"AI 分析失败"}}}}class f{constructor(){this.apiKey=process.env.TEXT_API_KEY||"",this.baseURL=process.env.TEXT_BASE_URL||"https://api.openai.com/v1",this.model=process.env.TEXT_MODEL||"gpt-4"}async generateSummary(a){try{let b=Object.keys(a).filter(b=>a[b]&&a[b].trim());if(0===b.length)return{success:!1,message:"没有学习内容可以总结"};let c=`请基于以下学习内容生成一份简洁、有条理的学习总结：

${b.map(b=>`${b}:
${a[b]}`).join("\n\n")}

请生成一份包含以下内容的总结：
1. 今日学习概览
2. 各科目重点内容
3. 知识点关联分析
4. 学习建议

总结应该简洁明了，重点突出，有助于复习和巩固。请使用纯文本格式，不要使用markdown语法。`,e=(await d.A.post(`${this.baseURL}/chat/completions`,{model:this.model,messages:[{role:"system",content:"你是一个专业的学习助手，擅长分析和总结学习内容，帮助学生更好地理解和记忆知识点。"},{role:"user",content:c}],max_tokens:1500,temperature:.7},{headers:{Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json"}})).data.choices[0].message.content;return{success:!0,summary:e}}catch(a){return console.error("AI 总结生成失败:",a),{success:!1,message:"AI 总结生成失败"}}}}let g=new e,h=new f}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55,612],()=>b(b.s=93209));module.exports=c})();