#!/bin/bash

# 学习管理系统 2.0 - 一行命令部署
# 使用默认配置快速启动

echo "🚀 启动学习管理系统 2.0..."

# 创建目录
mkdir -p notion-ai-study && cd notion-ai-study
mkdir -p uploads logs

# 提示用户输入必要配置
echo "请输入以下配置信息："
read -p "DockerHub 用户名: " DOCKERHUB_USERNAME
read -p "登录用户名 [admin]: " STUDY_USERNAME
STUDY_USERNAME=${STUDY_USERNAME:-admin}
read -s -p "登录密码: " STUDY_PASSWORD
echo
read -p "Notion Token: " NOTION_TOKEN
read -p "Notion Database ID: " DATABASE_ID
read -p "Vision API Key: " VISION_API_KEY
read -p "Text API Key: " TEXT_API_KEY

# 启动容器
echo "🚀 启动容器..."
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v "$(pwd)/uploads:/app/uploads" \
  -v "$(pwd)/logs:/app/logs" \
  -e STUDY_USERNAME="$STUDY_USERNAME" \
  -e STUDY_PASSWORD="$STUDY_PASSWORD" \
  -e NOTION_TOKEN="$NOTION_TOKEN" \
  -e DATABASE_ID="$DATABASE_ID" \
  -e VISION_API_KEY="$VISION_API_KEY" \
  -e VISION_BASE_URL="https://api.openai.com/v1" \
  -e VISION_MODEL="gpt-4-vision-preview" \
  -e TEXT_API_KEY="$TEXT_API_KEY" \
  -e TEXT_BASE_URL="https://api.openai.com/v1" \
  -e TEXT_MODEL="gpt-4" \
  -e NODE_ENV="production" \
  -e PORT="5002" \
  --restart unless-stopped \
  "${DOCKERHUB_USERNAME}/notion-ai-study:latest"

echo "✅ 部署完成！"
echo "📱 访问地址: http://localhost:5002"
echo "🔍 查看日志: docker logs -f notion-ai-study"
