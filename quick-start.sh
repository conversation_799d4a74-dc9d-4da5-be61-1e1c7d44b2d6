#!/bin/bash

# 学习管理系统 2.0 - 一键启动脚本
# Quick Start Script for Notion AI Study System

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 显示欢迎信息
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    学习管理系统 2.0                          ║"
echo "║                   一键启动部署脚本                           ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查 Docker 是否安装
echo -e "${BLUE}🔍 检查系统环境...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到 Docker，请先安装 Docker${NC}"
    echo -e "${YELLOW}安装指南: https://docs.docker.com/get-docker/${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ 错误: 未找到 Docker Compose，请先安装 Docker Compose${NC}"
    echo -e "${YELLOW}安装指南: https://docs.docker.com/compose/install/${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查环境变量文件
echo -e "${BLUE}📝 检查配置文件...${NC}"
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        echo -e "${YELLOW}⚠️  未找到 .env 文件，正在从 .env.example 创建...${NC}"
        cp .env.example .env
        echo -e "${GREEN}✅ 已创建 .env 文件${NC}"
        echo -e "${YELLOW}📝 请编辑 .env 文件配置你的参数${NC}"
        echo -e "${YELLOW}重要配置项：${NC}"
        echo -e "  - DOCKERHUB_USERNAME: 你的 DockerHub 用户名"
        echo -e "  - NOTION_TOKEN: Notion API Token"
        echo -e "  - DATABASE_ID: Notion 数据库 ID"
        echo -e "  - AI API 相关配置"
        echo ""
        echo -e "${YELLOW}是否现在编辑配置文件? (y/n)${NC}"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            ${EDITOR:-nano} .env
        fi
    else
        echo -e "${RED}❌ 错误: 未找到 .env.example 文件${NC}"
        exit 1
    fi
fi

# 创建必要的目录
echo -e "${BLUE}📁 创建必要目录...${NC}"
mkdir -p uploads logs
echo -e "${GREEN}✅ 目录创建完成${NC}"

# 选择部署模式
echo -e "${BLUE}🚀 选择部署模式:${NC}"
echo -e "  ${GREEN}1)${NC} 生产环境 (推荐)"
echo -e "  ${YELLOW}2)${NC} 开发环境"
echo -e "  ${BLUE}3)${NC} 本地构建"
echo ""
echo -e "${YELLOW}请选择 (1-3):${NC}"
read -r mode

case $mode in
    1)
        echo -e "${GREEN}🚀 启动生产环境...${NC}"
        docker-compose up -d
        ;;
    2)
        echo -e "${YELLOW}🛠️  启动开发环境...${NC}"
        docker-compose -f docker-compose.dev.yml up -d
        ;;
    3)
        echo -e "${BLUE}🔨 本地构建模式...${NC}"
        if [ -f "scripts/docker-build.sh" ]; then
            chmod +x scripts/docker-build.sh
            ./scripts/docker-build.sh
        else
            echo -e "${BLUE}🔨 构建 Docker 镜像...${NC}"
            docker build -t notion-ai-study:latest .
            echo -e "${GREEN}✅ 镜像构建完成${NC}"
            
            echo -e "${BLUE}🚀 启动容器...${NC}"
            docker run -d \
                --name notion-ai-study-app \
                -p 5002:5002 \
                -v "$(pwd)/uploads:/app/uploads" \
                -v "$(pwd)/logs:/app/logs" \
                --env-file .env \
                notion-ai-study:latest
        fi
        ;;
    *)
        echo -e "${RED}❌ 无效选择，退出${NC}"
        exit 1
        ;;
esac

# 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
if curl -f http://localhost:5002/api/health &> /dev/null; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${GREEN}📱 访问地址: http://localhost:5002${NC}"
    echo -e "${GREEN}🏥 健康检查: http://localhost:5002/api/health${NC}"
    echo ""
    echo -e "${BLUE}📊 常用命令:${NC}"
    echo -e "  查看日志: ${YELLOW}docker-compose logs -f${NC}"
    echo -e "  停止服务: ${YELLOW}docker-compose down${NC}"
    echo -e "  重启服务: ${YELLOW}docker-compose restart${NC}"
    echo -e "  查看状态: ${YELLOW}docker-compose ps${NC}"
else
    echo -e "${YELLOW}⚠️  服务可能还在启动中，请稍等片刻${NC}"
    echo -e "${YELLOW}📱 访问地址: http://localhost:5002${NC}"
    echo -e "${YELLOW}🔍 查看日志: docker-compose logs -f${NC}"
fi

echo ""
echo -e "${PURPLE}感谢使用学习管理系统 2.0！${NC}"
