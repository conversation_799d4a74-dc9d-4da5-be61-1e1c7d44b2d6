#!/bin/bash

# 学习管理系统 2.0 - 简化部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 学习管理系统 2.0 - 快速部署${NC}"

# 检查 Docker 环境
echo -e "${BLUE}🔍 检查 Docker 环境...${NC}"
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ 未找到 Docker，请先安装 Docker${NC}"
    echo -e "${YELLOW}安装指南: https://docs.docker.com/get-docker/${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ 未找到 Docker Compose${NC}"
    echo -e "${YELLOW}安装指南: https://docs.docker.com/compose/install/${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查配置文件
if [ ! -f ".env" ]; then
    if [ -f "config/app.env" ]; then
        echo -e "${YELLOW}📝 使用默认配置文件...${NC}"
        cp config/app.env .env
    else
        echo -e "${YELLOW}📥 下载配置模板...${NC}"
        curl -s -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/config/app.env
        cp app.env .env 2>/dev/null || {
            curl -s -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/.env.example
            cp .env.example .env
        }
    fi
    echo -e "${GREEN}✅ 已创建 .env 文件${NC}"
    echo -e "${YELLOW}⚠️  请编辑 .env 文件配置您的参数${NC}"
    echo -e "${YELLOW}是否现在编辑? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        ${EDITOR:-nano} .env
    fi
fi

# 创建必要目录
echo -e "${BLUE}📁 创建必要目录...${NC}"
mkdir -p uploads logs
echo -e "${GREEN}✅ 目录创建完成${NC}"

# 使用固定的镜像名
DOCKER_IMAGE="jhxxr/notion-ai-study:latest"

# 拉取最新镜像
echo -e "${BLUE}📦 拉取最新 Docker 镜像...${NC}"
docker pull ${DOCKER_IMAGE}

# 停止并删除旧容器（如果存在）
if [ "$(docker ps -aq -f name=notion-ai-study)" ]; then
    echo -e "${YELLOW}🔄 停止旧容器...${NC}"
    docker stop notion-ai-study 2>/dev/null || true
    docker rm notion-ai-study 2>/dev/null || true
fi

# 启动服务
echo -e "${GREEN}🚀 启动学习管理系统...${NC}"
docker run -d \
  --name notion-ai-study \
  -p 62211:5002 \
  -v "/root/notion-ai-study/uploads:/app/uploads" \
  -v "/root/notion-ai-study/logs:/app/logs" \
  --env-file .env \
  --restart unless-stopped \
  ${DOCKER_IMAGE}

# 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 15

# 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
if curl -f -s http://localhost:62211/api/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${GREEN}📱 访问地址: http://localhost:62211${NC}"
    echo -e "${GREEN}🏥 健康检查: http://localhost:62211/api/health${NC}"
    echo ""
    echo -e "${BLUE}📊 常用命令:${NC}"
    echo -e "  查看日志: ${YELLOW}docker logs -f notion-ai-study${NC}"
    echo -e "  停止服务: ${YELLOW}docker stop notion-ai-study${NC}"
    echo -e "  重启服务: ${YELLOW}docker restart notion-ai-study${NC}"
    echo -e "  删除容器: ${YELLOW}docker rm -f notion-ai-study${NC}"
    echo -e "  更新版本: ${YELLOW}./quick-start.sh${NC}"
else
    echo -e "${YELLOW}⚠️  服务可能还在启动中...${NC}"
    echo -e "${YELLOW}📱 请稍等片刻后访问: http://localhost:62211${NC}"
    echo -e "${YELLOW}🔍 查看启动日志: docker logs -f notion-ai-study${NC}"
fi

echo ""
echo -e "${PURPLE}感谢使用学习管理系统 2.0！${NC}"
echo -e "${BLUE}如有问题，请查看日志或访问项目文档${NC}"
