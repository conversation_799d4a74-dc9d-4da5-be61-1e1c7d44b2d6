#!/bin/bash

# Docker 构建脚本
# 用于本地开发和测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="notion-ai-study"
TAG=${1:-"latest"}
DOCKERFILE="Dockerfile"

echo -e "${BLUE}🐳 开始构建 Docker 镜像...${NC}"
echo -e "${YELLOW}镜像名称: ${IMAGE_NAME}:${TAG}${NC}"

# 检查 Dockerfile 是否存在
if [ ! -f "$DOCKERFILE" ]; then
    echo -e "${RED}❌ 错误: 找不到 Dockerfile${NC}"
    exit 1
fi

# 检查 package.json 是否存在
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ 错误: 找不到 package.json${NC}"
    exit 1
fi

# 构建镜像
echo -e "${BLUE}🔨 构建镜像...${NC}"
docker build \
    --tag "${IMAGE_NAME}:${TAG}" \
    --build-arg NODE_ENV=production \
    --progress=plain \
    .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 镜像构建成功!${NC}"
    echo -e "${GREEN}镜像: ${IMAGE_NAME}:${TAG}${NC}"
    
    # 显示镜像信息
    echo -e "${BLUE}📊 镜像信息:${NC}"
    docker images "${IMAGE_NAME}:${TAG}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # 询问是否运行容器
    echo -e "${YELLOW}是否要运行容器进行测试? (y/n)${NC}"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        echo -e "${BLUE}🚀 启动容器...${NC}"
        docker run -d \
            --name "${IMAGE_NAME}-test" \
            -p 5002:5002 \
            -v "$(pwd)/uploads:/app/uploads" \
            "${IMAGE_NAME}:${TAG}"
        
        echo -e "${GREEN}✅ 容器已启动!${NC}"
        echo -e "${GREEN}访问地址: http://localhost:5002${NC}"
        echo -e "${YELLOW}停止容器: docker stop ${IMAGE_NAME}-test${NC}"
        echo -e "${YELLOW}删除容器: docker rm ${IMAGE_NAME}-test${NC}"
    fi
else
    echo -e "${RED}❌ 镜像构建失败!${NC}"
    exit 1
fi
