# 依赖目录
node_modules
backend/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
backend/dist
.next
out

# 开发文件
.git
.gitignore
README.md
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 文件
.vscode
.idea
*.swp
*.swo
*~

# 日志文件
logs
*.log

# 运行时文件
pids
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage
.nyc_output

# 依赖锁定文件（保留 package-lock.json 用于 Docker 构建）
# package-lock.json  # 注释掉，Docker 构建需要这个文件
yarn.lock

# 临时文件
.tmp
.temp

# macOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db

# 测试文件
test
tests
__tests__
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# 文档
docs
*.md
!README.md

# 脚本（除了生产启动脚本）
scripts/dev.sh
scripts/build.sh

# 上传文件（在运行时挂载）
uploads/*
!uploads/.gitkeep
