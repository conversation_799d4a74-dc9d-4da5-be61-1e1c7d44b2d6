# 学习管理系统 2.0

[![Docker](https://img.shields.io/badge/Docker-Ready-blue?logo=docker)](https://hub.docker.com)
[![Next.js](https://img.shields.io/badge/Next.js-15.4.6-black?logo=next.js)](https://nextjs.org)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?logo=typescript)](https://www.typescriptlang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

一个基于 Next.js + TypeScript 的现代化学习管理系统，支持图片 AI 分析、手动录入、数据查看和智能总结功能。

## ⚡ 一键部署

### 🚀 超简单部署（推荐）

```bash
# 一行命令部署（交互式配置）
curl -sSL https://raw.githubusercontent.com/your-repo/notion-ai-study/main/one-line-deploy.sh | bash
```

### 🛠️ 直接命令行部署

```bash
# 创建目录
mkdir -p notion-ai-study && cd notion-ai-study
mkdir -p uploads logs

# 一键启动容器
docker run -d \
  --name notion-ai-study \
  -p 62211:5002 \
  -v /root/notion-ai-study/uploads:/app/uploads \
  -v /root/notion-ai-study/logs:/app/logs \
  -e STUDY_USERNAME=admin \
  -e STUDY_PASSWORD=your-password \
  -e NOTION_TOKEN=your-notion-token \
  -e DATABASE_ID=your-database-id \
  -e VISION_API_KEY=your-vision-api-key \
  -e VISION_BASE_URL=https://api.openai.com/v1 \
  -e VISION_MODEL=gpt-4-vision-preview \
  -e TEXT_API_KEY=your-text-api-key \
  -e TEXT_BASE_URL=https://api.openai.com/v1 \
  -e TEXT_MODEL=gpt-4 \
  -e NODE_ENV=production \
  -e PORT=5002 \
  jhxxr/notion-ai-study:latest

# 访问应用: http://localhost:5002
```

### 🛠️ 使用环境文件部署

```bash
# 下载环境变量模板
curl -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/.env.example
cp .env.example .env && nano .env

# 创建目录
mkdir -p uploads logs

# 使用环境文件启动
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  your-dockerhub-username/notion-ai-study:latest
```

## 🔧 环境变量配置

### 必需配置

```bash
# 用户认证
STUDY_USERNAME=admin
STUDY_PASSWORD=your-secure-password

# Notion 配置
NOTION_TOKEN=your-notion-integration-token
DATABASE_ID=your-notion-database-id

# AI 视觉模型配置
VISION_API_KEY=your-vision-api-key
VISION_BASE_URL=https://api.openai.com/v1
VISION_MODEL=gpt-4-vision-preview

# AI 文本模型配置  
TEXT_API_KEY=your-text-api-key
TEXT_BASE_URL=https://api.openai.com/v1
TEXT_MODEL=gpt-4
```

### 可选配置

```bash
# Docker 配置
DOCKERHUB_USERNAME=your-dockerhub-username
IMAGE_TAG=latest
HOST_PORT=5002

# 应用配置
NODE_ENV=production
PORT=5002
MAX_FILE_SIZE=10485760  # 10MB
```

## 🎯 主要功能

- **🖼️ 图片上传分析**: AI 自动识别学习内容，智能科目分类
- **✍️ 手动录入**: 科目分类录入，智能内容合并
- **📊 数据查看**: 今日学习数据展示，学习统计信息
- **🤖 AI 智能总结**: 基于学习内容生成总结和复盘建议
- **🔐 用户认证**: 安全的登录验证和会话管理
- **📱 响应式设计**: 支持深色/浅色主题，移动端适配

## 🔧 API 接口

- `GET /api/health` - 健康检查
- `POST /api/auth/login` - 用户登录
- `POST /api/upload` - 图片上传分析
- `POST /api/study/update` - 更新学习记录
- `GET /api/study/today` - 获取今日数据
- `POST /api/summary/generate` - 生成 AI 总结

## 🚀 快速命令

```bash
# 查看容器状态
docker ps -f name=notion-ai-study

# 查看日志
docker logs -f notion-ai-study

# 重启服务
docker restart notion-ai-study

# 停止服务
docker stop notion-ai-study

# 删除容器
docker rm -f notion-ai-study

# 更新到最新版本
docker pull your-dockerhub-username/notion-ai-study:latest
# 然后重新运行部署命令
```

## 🏥 健康检查

```bash
# 检查应用状态
curl http://localhost:5002/api/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "production",
  "version": "0.1.0"
}
```

## 🛠️ 技术栈

- **Next.js 15.4.6** - 全栈 React 框架
- **TypeScript** - 类型安全
- **TailwindCSS 4** - 原子化 CSS 框架
- **Docker** - 容器化部署
- **Notion API** - 数据存储
- **OpenAI API** - AI 分析和总结

## 📄 许可证

MIT License

---

**学习管理系统 2.0** - 让学习更智能，让记录更简单！
