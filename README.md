# 学习管理系统 2.0

一个基于 Next.js + TypeScript 的现代化学习管理系统，支持图片 AI 分析、手动录入、数据查看和智能总结功能。

## 🚀 技术栈

### 核心框架
- **Next.js 15.4.6** - 全栈 React 框架
- **React 19** - 现代化 UI 框架
- **TypeScript** - 类型安全
- **TailwindCSS 4** - 原子化 CSS 框架

### 功能特性
- **Multer** - 文件上传处理
- **Sharp** - 图片处理
- **Axios** - HTTP 客户端
- **Lucide React** - 图标库

### 集成服务
- **Notion API** - 数据存储和管理
- **AI 视觉识别** - 图片内容分析
- **AI 文本处理** - 内容分析和总结

### 部署支持
- **Docker** - 容器化部署
- **GitHub Actions** - 自动化 CI/CD
- **Multi-platform** - 支持 AMD64/ARM64

## 📁 项目结构

```
notion-ai-study/
├── src/                          # Next.js 源码
│   ├── app/                     # App Router 页面
│   │   ├── api/                 # API 路由
│   │   │   ├── auth/           # 认证接口
│   │   │   ├── health/         # 健康检查
│   │   │   ├── study/          # 学习记录接口
│   │   │   ├── summary/        # AI 总结接口
│   │   │   └── upload/         # 文件上传接口
│   │   ├── dashboard/          # 仪表板页面
│   │   ├── data/               # 数据查看页面
│   │   ├── login/              # 登录页面
│   │   ├── manual/             # 手动录入页面
│   │   ├── summary/            # AI 总结页面
│   │   ├── upload/             # 图片上传页面
│   │   ├── layout.tsx          # 根布局
│   │   ├── page.tsx            # 首页
│   │   └── globals.css         # 全局样式
│   ├── lib/                    # 工具库
│   └── types/                  # TypeScript 类型定义
├── public/                      # 静态资源
├── uploads/                     # 文件上传目录
├── logs/                       # 日志目录
├── scripts/                    # 构建脚本
├── .github/workflows/          # GitHub Actions
├── Dockerfile                  # Docker 构建文件
├── docker-compose.yml          # 生产环境配置
├── docker-compose.dev.yml      # 开发环境配置
├── .env.example               # 环境变量模板
├── package.json               # 项目配置
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 🐳 一键 Docker 部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd notion-ai-study

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入你的配置

# 3. 一键启动（生产环境）
docker-compose up -d

# 4. 查看运行状态
docker-compose ps

# 5. 访问应用
# http://localhost:5002
```

### 🛠️ 本地开发

#### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd notion-ai-study

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件
```

#### 2. 开发模式
```bash
# 启动开发服务器
npm run dev

# 访问 http://localhost:3000
```

#### 3. 生产构建
```bash
# 构建应用
npm run build

# 启动生产服务器
npm start
```

### 🐳 Docker 开发环境

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 查看日志
docker-compose -f docker-compose.dev.yml logs -f

# 停止服务
docker-compose -f docker-compose.dev.yml down
```

## 🎯 主要功能

### 1. 用户认证
- 安全的登录验证
- 会话状态管理
- 路由权限控制

### 2. 图片上传分析
- 支持拖拽上传
- 多种图片格式支持
- AI 自动识别学习内容
- 智能科目分类
- 自动同步到 Notion

### 3. 手动录入
- 科目分类录入
- 智能内容合并
- 实时字符统计
- 直接同步 Notion

### 4. 数据查看
- 今日学习数据展示
- 科目内容详情
- 学习统计信息
- 实时数据刷新

### 5. AI 智能总结
- 基于学习内容生成总结
- 智能复盘建议
- 自动保存到 Notion
- 支持重新生成

### 6. 响应式设计
- 支持深色/浅色主题
- 移动端适配
- 现代化 UI 设计
- 流畅的交互体验

## 🔧 API 接口

### 系统接口
- `GET /api/health` - 健康检查

### 认证接口
- `POST /api/auth/login` - 用户登录

### 学习记录接口
- `POST /api/upload` - 图片上传分析
- `POST /api/study/update` - 更新学习记录
- `GET /api/study/today` - 获取今日数据
- `POST /api/summary/generate` - 生成 AI 总结

## 🎨 UI 组件

项目包含完整的 UI 组件库：
- `Button` - 按钮组件
- `Input` - 输入框组件
- `Card` - 卡片组件
- `Loading` - 加载组件
- `Modal` - 模态框组件

所有组件支持：
- TypeScript 类型安全
- TailwindCSS 样式
- 深色主题适配
- 响应式设计

## 📱 页面路由

- `/login` - 登录页面
- `/dashboard` - 仪表板
- `/upload` - 图片上传分析
- `/manual` - 手动录入
- `/data` - 数据查看
- `/summary` - AI 总结

## 🔒 环境变量说明

| 变量名 | 说明 | 示例 |
|--------|------|------|
| `STUDY_USERNAME` | 登录用户名 | `admin` |
| `STUDY_PASSWORD` | 登录密码 | `password` |
| `NOTION_TOKEN` | Notion API Token | `secret_xxx` |
| `DATABASE_ID` | Notion 数据库 ID | `xxx-xxx-xxx` |
| `VISION_API_KEY` | 视觉 AI API Key | `sk-xxx` |
| `VISION_BASE_URL` | 视觉 AI API 地址 | `https://api.xxx.com/v1` |
| `TEXT_API_KEY` | 文本 AI API Key | `sk-xxx` |
| `TEXT_BASE_URL` | 文本 AI API 地址 | `https://api.xxx.com/v1` |

## 🚀 部署指南

### Docker 部署（推荐）

#### 快速部署
```bash
# 一键部署到生产环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 自定义配置
```bash
# 设置自定义端口和镜像
export HOST_PORT=8080
export IMAGE_TAG=v1.0.0
export DOCKERHUB_USERNAME=your-username

# 启动服务
docker-compose up -d
```

#### 本地构建部署
```bash
# 构建本地镜像
./scripts/docker-build.sh

# 使用本地镜像启动
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  notion-ai-study:latest
```

### GitHub Actions 自动部署

1. 在 GitHub 仓库设置中添加 Secrets：
   - `DOCKERHUB_USERNAME`: DockerHub 用户名
   - `DOCKERHUB_TOKEN`: DockerHub 访问令牌

2. 推送代码到 `main` 分支自动触发构建和部署

3. 在服务器上拉取最新镜像：
```bash
# 拉取最新镜像
docker-compose pull

# 重启服务
docker-compose up -d
```

### 健康检查

```bash
# 检查应用健康状态
curl http://localhost:5002/api/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "production",
  "version": "0.1.0"
}
```

### 故障排除

```bash
# 查看容器日志
docker-compose logs -f notion-ai-study

# 进入容器调试
docker exec -it notion-ai-study-app sh

# 重启服务
docker-compose restart

# 清理并重新部署
docker-compose down
docker-compose pull
docker-compose up -d
```

详细部署文档请参考：[DEPLOYMENT.md](./DEPLOYMENT.md)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**学习管理系统 2.0** - 让学习更智能，让记录更简单！
