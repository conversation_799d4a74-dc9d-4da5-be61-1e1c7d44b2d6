import axios from 'axios';

const NOTION_API_BASE = 'https://api.notion.com/v1';

interface NotionClient {
  token: string;
  databaseId: string;
}

class NotionService {
  private client: NotionClient;

  constructor() {
    this.client = {
      token: process.env.NOTION_TOKEN || '',
      databaseId: process.env.DATABASE_ID || ''
    };
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.client.token}`,
      'Content-Type': 'application/json',
      'Notion-Version': '2022-06-28'
    };
  }

  // 获取今日数据
  async getTodayData(): Promise<{ success: boolean; data?: any; date?: string; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const response = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      const results = response.data.results;
      
      if (results.length === 0) {
        return { success: true, data: {}, date: today };
      }

      const record = results[0];
      const data: Record<string, string> = {};

      // 提取各科目数据 - 考研科目
      const subjects = ['数学', '英语', '政治', '计算机408'];
      subjects.forEach(subject => {
        const property = record.properties[subject];
        if (property && property.rich_text && property.rich_text.length > 0) {
          data[subject] = property.rich_text[0].plain_text;
        }
      });

      return { success: true, data, date: today };
    } catch (error) {
      console.error('获取今日数据失败:', error);
      return { success: false, message: '获取数据失败' };
    }
  }

  // 更新学习记录
  async updateStudyRecord(subject: string, content: string, mode: 'replace' | 'append' = 'replace'): Promise<{ success: boolean; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // 查找今日记录
      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      let pageId: string;
      let existingContent = '';

      if (queryResponse.data.results.length > 0) {
        // 更新现有记录
        pageId = queryResponse.data.results[0].id;
        
        if (mode === 'append') {
          const existingProperty = queryResponse.data.results[0].properties[subject];
          if (existingProperty && existingProperty.rich_text && existingProperty.rich_text.length > 0) {
            existingContent = existingProperty.rich_text[0].plain_text;
          }
        }
      } else {
        // 创建新记录
        const createResponse = await axios.post(`${NOTION_API_BASE}/pages`, {
          parent: { database_id: this.client.databaseId },
          properties: {
            '日期': {
              date: { start: today }
            }
          }
        }, { headers: this.getHeaders() });
        
        pageId = createResponse.data.id;
      }

      // 更新内容
      const finalContent = mode === 'append' && existingContent 
        ? `${existingContent}\n\n${content}`
        : content;

      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {
        properties: {
          [subject]: {
            rich_text: [
              {
                text: { content: finalContent }
              }
            ]
          }
        }
      }, { headers: this.getHeaders() });

      return { success: true, message: '更新成功' };
    } catch (error) {
      console.error('更新学习记录失败:', error);
      return { success: false, message: '更新失败' };
    }
  }

  // 保存 AI 总结
  async saveSummary(summary: string): Promise<{ success: boolean; message?: string }> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // 查找今日记录
      const queryResponse = await axios.post(`${NOTION_API_BASE}/databases/${this.client.databaseId}/query`, {
        filter: {
          property: '日期',
          date: {
            equals: today
          }
        }
      }, { headers: this.getHeaders() });

      if (queryResponse.data.results.length === 0) {
        return { success: false, message: '未找到今日记录' };
      }

      const pageId = queryResponse.data.results[0].id;

      // 更新总结字段 - 使用正确的字段名称
      await axios.patch(`${NOTION_API_BASE}/pages/${pageId}`, {
        properties: {
          '总结（AI生成）': {
            rich_text: [
              {
                text: { content: summary.substring(0, 2000) } // 限制长度防止超出Notion限制
              }
            ]
          }
        }
      }, { headers: this.getHeaders() });

      return { success: true, message: '总结保存成功' };
    } catch (error) {
      console.error('保存总结失败:', error);
      return { success: false, message: '保存总结失败' };
    }
  }
}

export const notionService = new NotionService();
