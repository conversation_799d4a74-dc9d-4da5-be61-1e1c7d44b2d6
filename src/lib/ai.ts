import axios from 'axios';

// AI 视觉识别服务
export class VisionAI {
  private apiKey: string;
  private baseURL: string;

  constructor() {
    this.apiKey = process.env.VISION_API_KEY || '';
    this.baseURL = process.env.VISION_BASE_URL || '';
  }

  async analyzeImage(imageBase64: string): Promise<{ success: boolean; analysis?: any; message?: string }> {
    try {
      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `请分析这张图片中的学习内容，识别出具体的科目和内容。请按照以下格式返回JSON：

{
  "subject": "科目名称（数学/英语/政治/计算机408中的一个）",
  "content": "## 📸 图片分析记录 - ${new Date().toISOString().split('T')[0]}\n\n**科目：** [科目名称]\n\n**学习内容：**\n\n[详细的学习内容描述]\n\n**图片描述：**\n\n[对图片内容的详细描述]",
  "confidence": 0.95
}

请确保内容详细且有条理，图片描述要准确反映图片中的具体内容。`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageBase64}`
                }
              }
            ]
          }
        ],
        max_tokens: 1000
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const content = response.data.choices[0].message.content;
      
      try {
        const analysis = JSON.parse(content);
        return { success: true, analysis };
      } catch {
        // 如果不是 JSON 格式，返回原始文本
        return { 
          success: true, 
          analysis: { 
            subject: '未知', 
            content: content, 
            confidence: 0.5 
          } 
        };
      }
    } catch (error) {
      console.error('AI 视觉分析失败:', error);
      return { success: false, message: 'AI 分析失败' };
    }
  }
}

// AI 文本处理服务
export class TextAI {
  private apiKey: string;
  private baseURL: string;

  constructor() {
    this.apiKey = process.env.TEXT_API_KEY || '';
    this.baseURL = process.env.TEXT_BASE_URL || '';
  }

  async generateSummary(studyData: Record<string, string>): Promise<{ success: boolean; summary?: string; message?: string }> {
    try {
      const subjects = Object.keys(studyData).filter(key => studyData[key] && studyData[key].trim());
      
      if (subjects.length === 0) {
        return { success: false, message: '没有学习内容可以总结' };
      }

      const prompt = `请基于以下学习内容生成一份简洁、有条理的学习总结：

${subjects.map(subject => `${subject}:\n${studyData[subject]}`).join('\n\n')}

请生成一份包含以下内容的总结：
1. 今日学习概览
2. 各科目重点内容
3. 知识点关联分析
4. 学习建议

总结应该简洁明了，重点突出，有助于复习和巩固。请使用纯文本格式，不要使用markdown语法。`;

      const response = await axios.post(`${this.baseURL}/chat/completions`, {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的学习助手，擅长分析和总结学习内容，帮助学生更好地理解和记忆知识点。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.7
      }, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      const summary = response.data.choices[0].message.content;
      return { success: true, summary };
    } catch (error) {
      console.error('AI 总结生成失败:', error);
      return { success: false, message: 'AI 总结生成失败' };
    }
  }
}

export const visionAI = new VisionAI();
export const textAI = new TextAI();
