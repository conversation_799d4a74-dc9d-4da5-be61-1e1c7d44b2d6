import { NextRequest } from 'next/server';

export interface User {
  username: string;
}

export function verifyAuth(request: NextRequest): User | null {
  try {
    const token = request.cookies.get('auth-token')?.value;
    
    if (!token) {
      return null;
    }

    // 解码令牌
    const decoded = Buffer.from(token, 'base64').toString();
    const [username, timestamp] = decoded.split(':');
    
    // 检查令牌是否过期（24小时）
    const tokenTime = parseInt(timestamp);
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    if (now - tokenTime > maxAge) {
      return null;
    }

    // 验证用户名
    const validUsername = process.env.STUDY_USERNAME || 'admin';
    if (username !== validUsername) {
      return null;
    }

    return { username };
  } catch (error) {
    console.error('验证令牌错误:', error);
    return null;
  }
}

export function requireAuth(request: NextRequest) {
  const user = verifyAuth(request);
  if (!user) {
    throw new Error('未授权访问');
  }
  return user;
}
