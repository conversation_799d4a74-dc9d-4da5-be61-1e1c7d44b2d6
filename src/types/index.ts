// 用户类型
export interface User {
  username: string;
}

// 应用状态类型
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark';
}

// 科目列表 - 考研科目
export const SUBJECTS = [
  '数学', '英语', '政治', '计算机408'
] as const;

export type Subject = typeof SUBJECTS[number];

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
}

// 登录请求类型
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应类型
export interface LoginResponse {
  success: boolean;
  message: string;
  user?: User;
  token?: string;
}

// 学习记录更新请求类型
export interface UpdateStudyRequest {
  subject: Subject | '其他';
  content: string;
  mode?: 'replace' | 'append';
}

// 今日数据响应类型
export interface TodayDataResponse {
  success: boolean;
  data?: Record<string, string>;
  date?: string;
  statistics?: {
    totalSubjects: number;
    hasContentCount: number;
    emptyCount: number;
    totalCharacters: number;
    completionRate: number;
  };
  message?: string;
}

// AI 分析结果类型
export interface AIAnalysis {
  subject: string;
  content: string;
  confidence: number;
}

// 图片上传响应类型
export interface UploadResponse {
  success: boolean;
  message: string;
  data?: {
    analysis: AIAnalysis;
    saved: boolean;
  };
}

// AI 总结响应类型
export interface SummaryResponse {
  success: boolean;
  summary?: string;
  saved?: boolean;
  message?: string;
  data?: {
    date: string;
    subjectsCount: number;
    totalCharacters: number;
  };
}

// 主题类型
export type Theme = 'light' | 'dark';

// 页面路由类型
export interface PageRoute {
  path: string;
  name: string;
  icon: any; // Lucide React 图标
  description: string;
}
