'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  Brain, 
  Sparkles, 
  FileText,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Copy,
  BookOpen
} from 'lucide-react';

interface SummaryData {
  date: string;
  subjectsCount: number;
  totalCharacters: number;
}

export default function SummaryPage() {
  const [summary, setSummary] = useState<string>('');
  const [summaryData, setSummaryData] = useState<SummaryData | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);
  
  const router = useRouter();

  // 生成AI总结
  const generateSummary = async () => {
    setIsGenerating(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/summary/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (data.success && data.summary) {
        setSummary(data.summary);
        setSummaryData(data.data);
        setHasGenerated(true);
        setSuccess(data.saved ? 
          'AI 总结生成成功并已保存到 Notion！' : 
          'AI 总结生成成功，但保存到 Notion 失败'
        );
      } else {
        setError(data.message || '生成总结失败');
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsGenerating(false);
    }
  };

  // 复制总结到剪贴板
  const copySummary = async () => {
    try {
      await navigator.clipboard.writeText(summary);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (error) {
      setError('复制失败，请手动选择文本复制');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <button
              onClick={() => router.push('/dashboard')}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>返回</span>
            </button>
            <h1 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
              AI 学习总结
            </h1>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 数据概览 */}
          {summaryData && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                  {summaryData.subjectsCount}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">已学科目</div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                  {summaryData.totalCharacters}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">总字符数</div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                  {hasGenerated ? '1' : '0'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">AI 总结</div>
              </div>
            </div>
          )}

          {/* 生成总结区域 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <div className="flex items-center mb-4">
              <Brain className="w-6 h-6 text-purple-600 dark:text-purple-400 mr-3" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                生成学习总结
              </h2>
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              AI 将基于您今日的学习内容生成一份简洁、有条理的总结，
              帮助您进行学习复盘和知识梳理。
            </p>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={generateSummary}
                disabled={isGenerating}
                className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="w-4 h-4 animate-spin" />
                    <span>生成中...</span>
                  </>
                ) : (
                  <>
                    <Brain className="w-4 h-4" />
                    <span>{hasGenerated ? '重新生成' : '生成总结'}</span>
                  </>
                )}
              </button>
              
              {hasGenerated && (
                <button
                  onClick={copySummary}
                  className="flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Copy className="w-4 h-4" />
                  <span>{copySuccess ? '已复制!' : '复制总结'}</span>
                </button>
              )}
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </div>
            </div>
          )}

          {/* 成功提示 */}
          {success && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                <p className="text-green-700 dark:text-green-400">{success}</p>
              </div>
            </div>
          )}

          {/* 总结结果展示 */}
          {summary && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
              <div className="flex items-center mb-4">
                <Sparkles className="w-6 h-6 text-purple-600 dark:text-purple-400 mr-3" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  AI 总结结果
                </h3>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap">
                    {summary}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 使用说明 */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div className="flex items-center mb-4">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400 mr-3" />
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
                使用说明
              </h3>
            </div>
            
            <div className="space-y-4 text-sm text-blue-700 dark:text-blue-300">
              <div>
                <h4 className="font-medium mb-2">🤖 AI 总结功能</h4>
                <p className="mb-2">
                  AI 会分析您今日在各个科目的学习内容，生成一份简洁、有条理的学习总结，帮助您：
                </p>
                <ul className="list-disc list-inside space-y-1 ml-4">
                  <li>回顾今日学习的重点内容</li>
                  <li>梳理各科目的知识要点</li>
                  <li>发现学习中的关联性</li>
                  <li>为明日学习提供参考</li>
                </ul>
              </div>

              <div>
                <h4 className="font-medium mb-2">💾 自动保存</h4>
                <p>
                  生成的总结会自动保存到您的 Notion 数据库中，
                  与当日的学习记录关联，方便后续查看和复习。
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">🔄 重新生成</h4>
                <p>
                  如果您对当前的总结不满意，可以点击"重新生成"按钮，
                  AI 会基于最新的学习内容重新生成总结。
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
