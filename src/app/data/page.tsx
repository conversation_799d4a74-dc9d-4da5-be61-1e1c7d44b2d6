'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  ArrowLeft, 
  RefreshCw, 
  Calendar, 
  BookOpen,
  AlertCircle,
  CheckCircle,
  BarChart3
} from 'lucide-react';
import { SUBJECTS } from '@/types';

interface TodayData {
  [key: string]: string;
}

interface Statistics {
  totalSubjects: number;
  hasContentCount: number;
  emptyCount: number;
  totalCharacters: number;
  completionRate: number;
}

export default function DataPage() {
  const [todayData, setTodayData] = useState<TodayData | null>(null);
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [currentDate, setCurrentDate] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [error, setError] = useState('');
  
  const router = useRouter();

  // 加载今日数据
  const loadTodayData = async (showLoading = true) => {
    if (showLoading) {
      setIsLoading(true);
    } else {
      setIsRefreshing(true);
    }
    setError('');

    try {
      const response = await fetch('/api/study/today');
      const data = await response.json();
      
      if (data.success) {
        setTodayData(data.data || {});
        setStatistics(data.statistics);
        setCurrentDate(data.date || new Date().toISOString().split('T')[0]);
      } else {
        setError(data.message || '获取数据失败');
        setTodayData(null);
        setStatistics(null);
      }
    } catch (error) {
      setError('网络错误，请稍后重试');
      setTodayData(null);
      setStatistics(null);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
      setLastRefresh(new Date());
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadTodayData();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">加载数据中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 顶部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.push('/dashboard')}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>返回</span>
              </button>
              <h1 className="ml-4 text-xl font-bold text-gray-900 dark:text-white">
                今日学习数据
              </h1>
            </div>
            
            <button
              onClick={() => loadTodayData(false)}
              disabled={isRefreshing}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-900/30 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span>刷新</span>
            </button>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* 日期和统计信息 */}
          <div className="mb-8">
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex items-center space-x-2 text-lg font-semibold text-gray-900 dark:text-white">
                <Calendar className="w-5 h-5" />
                <span>{currentDate}</span>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                最后更新: {lastRefresh.toLocaleTimeString()}
              </div>
            </div>
            
            {/* 统计卡片 */}
            {statistics && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                    {statistics.totalSubjects}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">总科目数</div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                    {statistics.hasContentCount}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">已有内容</div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                    {statistics.completionRate}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">完成率</div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-1">
                    {statistics.totalCharacters}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">总字符数</div>
                </div>
              </div>
            )}
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-3" />
                <p className="text-red-700 dark:text-red-400">{error}</p>
              </div>
            </div>
          )}

          {/* 学习内容展示 */}
          {todayData ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {SUBJECTS.map((subject) => {
                const content = todayData[subject];
                const hasContent = content && content.trim();
                
                return (
                  <div key={subject} className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {subject}
                      </h3>
                      {hasContent ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <AlertCircle className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    
                    <div className="min-h-[120px]">
                      {hasContent ? (
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <pre className="whitespace-pre-wrap text-sm text-gray-900 dark:text-gray-100 font-sans">
                            {content}
                          </pre>
                        </div>
                      ) : (
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex items-center justify-center">
                          <div className="text-center text-gray-500 dark:text-gray-400">
                            <BookOpen className="w-8 h-8 mx-auto mb-2 opacity-50" />
                            <p className="text-sm">今日还未录入{subject}内容</p>
                            <p className="text-xs mt-1">
                              可通过图片上传或手动录入添加内容
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* 字符统计 */}
                    {hasContent && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 text-right mt-2">
                        {content.length} 字符
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          ) : (
            /* 无数据状态 */
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-12 text-center">
              <BarChart3 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                暂无今日学习记录
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                开始记录您的学习内容吧！
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  onClick={() => router.push('/upload')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  图片上传分析
                </button>
                <button
                  onClick={() => router.push('/manual')}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  手动录入内容
                </button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
