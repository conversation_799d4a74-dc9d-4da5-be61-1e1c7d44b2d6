import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { visionAI } from '@/lib/ai';
import { notionService } from '@/lib/notion';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    const formData = await request.formData();
    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({
        success: false,
        message: '未找到上传的图片'
      }, { status: 400 });
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        success: false,
        message: '只支持图片文件'
      }, { status: 400 });
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        success: false,
        message: '图片文件过大，请选择小于10MB的图片'
      }, { status: 400 });
    }

    // 读取文件内容
    const buffer = Buffer.from(await file.arrayBuffer());

    // 使用 Sharp 处理图片
    const processedImage = await sharp(buffer)
      .resize(1024, 1024, { 
        fit: 'inside',
        withoutEnlargement: true 
      })
      .jpeg({ quality: 80 })
      .toBuffer();

    // 转换为 base64
    const base64Image = processedImage.toString('base64');

    // AI 分析图片
    const analysisResult = await visionAI.analyzeImage(base64Image);

    if (!analysisResult.success) {
      return NextResponse.json({
        success: false,
        message: analysisResult.message || 'AI 分析失败'
      }, { status: 500 });
    }

    const { analysis } = analysisResult;
    
    // 保存到 Notion
    const saveResult = await notionService.updateStudyRecord(
      analysis.subject || '其他',
      analysis.content || '图片内容分析',
      'append'
    );

    if (!saveResult.success) {
      return NextResponse.json({
        success: false,
        message: saveResult.message || '保存到 Notion 失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '图片上传和分析成功',
      data: {
        analysis,
        saved: true
      }
    });

  } catch (error) {
    console.error('图片上传处理错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
