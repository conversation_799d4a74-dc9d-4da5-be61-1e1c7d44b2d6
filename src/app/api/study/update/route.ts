import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { notionService } from '@/lib/notion';

export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    const { subject, content, mode = 'replace' } = await request.json();

    // 验证输入
    if (!subject || !content) {
      return NextResponse.json({
        success: false,
        message: '科目和内容不能为空'
      }, { status: 400 });
    }

    // 验证科目
    const validSubjects = ['语文', '数学', '英语', '物理', '化学', '生物', '历史', '地理', '政治', '其他'];
    if (!validSubjects.includes(subject)) {
      return NextResponse.json({
        success: false,
        message: '无效的科目'
      }, { status: 400 });
    }

    // 验证模式
    if (!['replace', 'append'].includes(mode)) {
      return NextResponse.json({
        success: false,
        message: '无效的更新模式'
      }, { status: 400 });
    }

    // 更新 Notion 记录
    const result = await notionService.updateStudyRecord(subject, content, mode);

    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || '更新失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '学习记录更新成功',
      data: {
        subject,
        content,
        mode,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('更新学习记录错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
