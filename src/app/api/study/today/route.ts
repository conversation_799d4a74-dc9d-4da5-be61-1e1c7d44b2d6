import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { notionService } from '@/lib/notion';

export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    requireAuth(request);

    // 获取今日数据
    const result = await notionService.getTodayData();

    if (!result.success) {
      return NextResponse.json({
        success: false,
        message: result.message || '获取数据失败'
      }, { status: 500 });
    }

    // 计算统计信息 - 考研科目
    const data = result.data || {};
    const subjects = ['数学', '英语', '政治', '计算机408'];

    let totalSubjects = 0;
    let hasContentCount = 0;
    let totalCharacters = 0;

    subjects.forEach(subject => {
      totalSubjects++;
      if (data[subject] && data[subject].trim()) {
        hasContentCount++;
        totalCharacters += data[subject].length;
      }
    });

    const statistics = {
      totalSubjects,
      hasContentCount,
      emptyCount: totalSubjects - hasContentCount,
      totalCharacters,
      completionRate: Math.round((hasContentCount / totalSubjects) * 100)
    };

    return NextResponse.json({
      success: true,
      data: result.data,
      date: result.date,
      statistics,
      message: '获取今日数据成功'
    });

  } catch (error) {
    console.error('获取今日数据错误:', error);
    
    if (error instanceof Error && error.message === '未授权访问') {
      return NextResponse.json({
        success: false,
        message: '未授权访问'
      }, { status: 401 });
    }

    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
