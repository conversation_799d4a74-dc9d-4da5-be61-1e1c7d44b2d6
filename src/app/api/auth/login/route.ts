import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();

    // 验证用户名和密码
    const validUsername = process.env.STUDY_USERNAME || 'admin';
    const validPassword = process.env.STUDY_PASSWORD || 'password';

    if (username === validUsername && password === validPassword) {
      // 创建简单的会话令牌
      const token = Buffer.from(`${username}:${Date.now()}`).toString('base64');
      
      const response = NextResponse.json({
        success: true,
        message: '登录成功',
        user: { username },
        token
      });

      // 设置 HTTP-only cookie
      response.cookies.set('auth-token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24小时
      });

      return response;
    } else {
      return NextResponse.json({
        success: false,
        message: '用户名或密码错误'
      }, { status: 401 });
    }
  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json({
      success: false,
      message: '服务器错误'
    }, { status: 500 });
  }
}
