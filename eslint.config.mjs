import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // 忽略未使用的变量警告
      "@typescript-eslint/no-unused-vars": "off",
      // 忽略 any 类型错误
      "@typescript-eslint/no-explicit-any": "off",
      // 忽略 React 转义字符错误
      "react/no-unescaped-entities": "off",
      // 忽略 img 元素警告
      "@next/next/no-img-element": "off",
    },
  },
];

export default eslintConfig;
