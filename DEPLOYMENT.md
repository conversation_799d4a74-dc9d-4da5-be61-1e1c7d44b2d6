# 部署指南

## 🚀 快速部署（推荐）

### 方式一：直接命令行部署

```bash
# 创建目录
mkdir -p notion-ai-study && cd notion-ai-study
mkdir -p uploads logs

# 一键启动容器
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  -e STUDY_USERNAME=admin \
  -e STUDY_PASSWORD=your-password \
  -e NOTION_TOKEN=your-notion-token \
  -e DATABASE_ID=your-database-id \
  -e VISION_API_KEY=your-vision-api-key \
  -e VISION_BASE_URL=https://api.openai.com/v1 \
  -e VISION_MODEL=gpt-4-vision-preview \
  -e TEXT_API_KEY=your-text-api-key \
  -e TEXT_BASE_URL=https://api.openai.com/v1 \
  -e TEXT_MODEL=gpt-4 \
  -e NODE_ENV=production \
  -e PORT=5002 \
  --restart unless-stopped \
  your-dockerhub-username/notion-ai-study:latest

# 访问应用: http://localhost:5002
```

### 方式二：使用环境文件部署

```bash
# 下载环境变量模板
curl -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/.env.example
cp .env.example .env && nano .env

# 创建目录
mkdir -p uploads logs

# 使用环境文件启动
docker run -d \
  --name notion-ai-study \
  -p 5002:5002 \
  -v $(pwd)/uploads:/app/uploads \
  -v $(pwd)/logs:/app/logs \
  --env-file .env \
  --restart unless-stopped \
  your-dockerhub-username/notion-ai-study:latest
```

### 方式三：使用一键脚本部署

```bash
# 下载并运行一键部署脚本
curl -O https://raw.githubusercontent.com/your-repo/notion-ai-study/main/quick-start.sh
chmod +x quick-start.sh
./quick-start.sh
```

## ⚙️ 环境变量配置

### 必需配置

```bash
# 用户认证
STUDY_USERNAME=admin
STUDY_PASSWORD=your-secure-password

# Notion 配置
NOTION_TOKEN=your-notion-integration-token
DATABASE_ID=your-notion-database-id

# AI 视觉模型配置
VISION_API_KEY=your-vision-api-key
VISION_BASE_URL=https://api.openai.com/v1
VISION_MODEL=gpt-4-vision-preview

# AI 文本模型配置  
TEXT_API_KEY=your-text-api-key
TEXT_BASE_URL=https://api.openai.com/v1
TEXT_MODEL=gpt-4
```

### 可选配置

```bash
# Docker 配置
DOCKERHUB_USERNAME=your-dockerhub-username
IMAGE_TAG=latest
HOST_PORT=5002

# 应用配置
NODE_ENV=production
PORT=5002
MAX_FILE_SIZE=10485760  # 10MB
```

## 🔧 常用命令

```bash
# 查看容器状态
docker ps -f name=notion-ai-study

# 查看日志
docker logs -f notion-ai-study

# 重启服务
docker restart notion-ai-study

# 停止服务
docker stop notion-ai-study

# 删除容器
docker rm -f notion-ai-study

# 更新到最新版本
docker pull your-dockerhub-username/notion-ai-study:latest
docker stop notion-ai-study
docker rm notion-ai-study
# 然后重新运行 docker run 命令

# 快速更新（使用脚本）
./quick-start.sh
```

## 🏥 健康检查

```bash
# 检查应用状态
curl http://localhost:5002/api/health

# 预期响应
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456,
  "environment": "production",
  "version": "0.1.0"
}
```

## 🛠️ 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 修改 .env 文件中的 HOST_PORT
   HOST_PORT=8080
   ```

2. **权限问题**
   ```bash
   # 确保目录权限正确
   sudo chown -R 1001:1001 uploads logs
   ```

3. **AI API 配置错误**
   ```bash
   # 检查 API Key 和 Base URL 是否正确
   # 确保 API Key 有足够的权限
   ```

### 调试命令

```bash
# 进入容器调试
docker exec -it notion-ai-study-app sh

# 查看容器资源使用
docker stats notion-ai-study-app

# 查看详细日志
docker-compose logs --tail=100 -f
```

## 🔒 安全建议

1. 更改默认用户名和密码
2. 使用强密码和安全的 API Key
3. 定期更新 Docker 镜像
4. 配置防火墙规则
5. 使用 HTTPS（建议配置反向代理）

## 📊 监控

### 应用监控

- 健康检查端点：`/api/health`
- 日志文件：`./logs/` 目录
- 上传文件：`./uploads/` 目录

### 性能监控

```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
du -sh uploads/ logs/

# 查看内存使用
docker exec notion-ai-study-app free -h
```
