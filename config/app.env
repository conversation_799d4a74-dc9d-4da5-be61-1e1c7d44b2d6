# 学习管理系统 2.0 - 应用配置文件
# 复制此文件为 .env 并修改相应配置

# 基础配置
NODE_ENV=production
PORT=5002
NEXT_TELEMETRY_DISABLED=1

# 用户认证
STUDY_USERNAME=admin
STUDY_PASSWORD=change-this-password

# Notion 配置
NOTION_TOKEN=your-notion-integration-token
DATABASE_ID=your-notion-database-id

# AI 视觉模型配置
VISION_API_KEY=your-vision-api-key
VISION_BASE_URL=https://api.openai.com/v1
VISION_MODEL=gpt-4-vision-preview

# AI 文本模型配置  
TEXT_API_KEY=your-text-api-key
TEXT_BASE_URL=https://api.openai.com/v1
TEXT_MODEL=gpt-4

# 文件配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=/app/uploads

# 日志配置
LOG_LEVEL=info
LOG_DIR=/app/logs

# Docker 配置（可选）
DOCKERHUB_USERNAME=your-dockerhub-username
IMAGE_TAG=latest
HOST_PORT=5002
